#!/usr/bin/env python3
"""Test SV_LIST parameter functionality"""

import os
import tempfile
import unittest
from pathlib import Path

from rtl2gds import Chip


class TestSVList(unittest.TestCase):
    """Test cases for SV_LIST parameter handling"""

    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_rtl_file = os.path.join(self.temp_dir, "test.v")
        self.test_sv_file1 = os.path.join(self.temp_dir, "test1.sv")
        self.test_sv_file2 = os.path.join(self.temp_dir, "test2.sv")
        self.test_filelist = os.path.join(self.temp_dir, "files.f")
        
        # Create test files
        with open(self.test_rtl_file, "w") as f:
            f.write("module test(); endmodule\n")
        
        with open(self.test_sv_file1, "w") as f:
            f.write("module test1(); endmodule\n")
            
        with open(self.test_sv_file2, "w") as f:
            f.write("module test2(); endmodule\n")
            
        with open(self.test_filelist, "w") as f:
            f.write(f"{self.test_sv_file1}\n{self.test_sv_file2}\n")

    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_sv_list_empty(self):
        """Test SV_LIST when not specified"""
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        chip = Chip(config_dict=config)
        self.assertEqual(chip.path_setting.sv_list, "")

    def test_sv_list_legacy_enable(self):
        """Test SV_LIST with legacy 'enable' flag"""
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            "SV_LIST": "enable",
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        chip = Chip(config_dict=config)
        # Legacy mode should result in empty string to trigger retroSoC template
        self.assertEqual(chip.path_setting.sv_list, "")

    def test_sv_list_single_file(self):
        """Test SV_LIST with single file path"""
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            "SV_LIST": self.test_sv_file1,
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        chip = Chip(config_dict=config)
        self.assertEqual(chip.path_setting.sv_list, os.path.abspath(self.test_sv_file1))

    def test_sv_list_multiple_files(self):
        """Test SV_LIST with multiple file paths"""
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            "SV_LIST": [self.test_sv_file1, self.test_sv_file2],
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        chip = Chip(config_dict=config)
        expected = [os.path.abspath(self.test_sv_file1), os.path.abspath(self.test_sv_file2)]
        self.assertEqual(chip.path_setting.sv_list, expected)

    def test_sv_list_file_not_found(self):
        """Test SV_LIST with non-existent file"""
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            "SV_LIST": "/non/existent/file.sv",
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        with self.assertRaises(FileNotFoundError):
            Chip(config_dict=config)

    def test_sv_list_to_env_dict_single_file(self):
        """Test SV_LIST conversion to environment dict with single file"""
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            "SV_LIST": self.test_sv_file1,
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        chip = Chip(config_dict=config)
        env_dict = chip.path_setting.to_env_dict()
        self.assertEqual(env_dict["SV_LIST"], os.path.abspath(self.test_sv_file1))

    def test_sv_list_to_env_dict_multiple_files(self):
        """Test SV_LIST conversion to environment dict with multiple files"""
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            "SV_LIST": [self.test_sv_file1, self.test_sv_file2],
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        chip = Chip(config_dict=config)
        env_dict = chip.path_setting.to_env_dict()
        expected = f"{os.path.abspath(self.test_sv_file1)} \n {os.path.abspath(self.test_sv_file2)}"
        self.assertEqual(env_dict["SV_LIST"], expected)


if __name__ == "__main__":
    unittest.main()
