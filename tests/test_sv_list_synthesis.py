#!/usr/bin/env python3
"""Test SV_LIST parameter in synthesis workflow"""

import os
import tempfile
import unittest
from pathlib import Path

from rtl2gds import Chip
from rtl2gds.flow.step_wrapper import StepWrapper


class TestSVListSynthesis(unittest.TestCase):
    """Test SV_LIST parameter in synthesis workflow"""

    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_rtl_file = os.path.join(self.temp_dir, "test.v")
        self.test_sv_file = os.path.join(self.temp_dir, "test.sv")
        
        # Create test files
        with open(self.test_rtl_file, "w") as f:
            f.write("module test(input clk, input rst, output reg out); always @(posedge clk) out <= ~rst; endmodule\n")
        
        with open(self.test_sv_file, "w") as f:
            f.write("interface test_if; logic valid; logic ready; endinterface\n")

    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_sv_list_single_file_generation(self):
        """Test SV_LIST single file filelist generation"""
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            "SV_LIST": self.test_sv_file,
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        chip = Chip(config_dict=config)
        wrapper = StepWrapper(chip)
        
        # Test filelist generation
        sv_list_result = wrapper._generate_sv_filelist()
        
        # Should return the file path directly for single file
        self.assertEqual(sv_list_result, os.path.abspath(self.test_sv_file))

    def test_sv_list_multiple_files_generation(self):
        """Test SV_LIST multiple files filelist generation"""
        test_sv_file2 = os.path.join(self.temp_dir, "test2.sv")
        with open(test_sv_file2, "w") as f:
            f.write("module test2(); endmodule\n")
            
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            "SV_LIST": [self.test_sv_file, test_sv_file2],
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        chip = Chip(config_dict=config)
        wrapper = StepWrapper(chip)
        
        # Test filelist generation
        sv_list_result = wrapper._generate_sv_filelist()
        
        # Should create a filelist file
        self.assertTrue(os.path.exists(sv_list_result))
        self.assertTrue(sv_list_result.endswith("_sv.f"))
        
        # Check filelist content
        with open(sv_list_result, "r") as f:
            content = f.read()
            self.assertIn(os.path.abspath(self.test_sv_file), content)
            self.assertIn(os.path.abspath(test_sv_file2), content)

    def test_sv_list_empty_legacy_mode(self):
        """Test SV_LIST empty (legacy mode) behavior"""
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            "SV_LIST": "enable",  # Legacy mode
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        chip = Chip(config_dict=config)
        wrapper = StepWrapper(chip)
        
        # Test filelist generation - should try retroSoC mode
        sv_list_result = wrapper._generate_sv_filelist()
        
        # In test environment without retroSoC template, should return empty string
        self.assertEqual(sv_list_result, "")

    def test_sv_list_not_configured(self):
        """Test SV_LIST when not configured"""
        config = {
            "TOP_NAME": "test",
            "RTL_FILE": self.test_rtl_file,
            # No SV_LIST specified
            "CLK_PORT_NAME": "clk",
            "CLK_FREQ_MHZ": 100,
            "RESULT_DIR": self.temp_dir,
        }
        chip = Chip(config_dict=config)
        wrapper = StepWrapper(chip)
        
        # Test filelist generation
        sv_list_result = wrapper._generate_sv_filelist()
        
        # Should return empty string when not configured
        self.assertEqual(sv_list_result, "")


if __name__ == "__main__":
    unittest.main()
