# Example configurations showing different SV_LIST usage patterns

# Example 1: Using a filelist file (recommended)
example_filelist:
  TOP_NAME: my_design
  RTL_FILE: ./src/my_design.v
  SV_LIST: ./src/sv_sources.f  # Path to filelist file containing SystemVerilog sources
  CLK_PORT_NAME: clk
  CLK_FREQ_MHZ: 100
  RESULT_DIR: ./results

# Example 2: Using a different filelist extension
example_list_extension:
  TOP_NAME: my_design
  RTL_FILE: ./src/my_design.v
  SV_LIST: ./src/sources.list  # Filelist with .list extension
  CLK_PORT_NAME: clk
  CLK_FREQ_MHZ: 100
  RESULT_DIR: ./results

# Example 3: Legacy retroSoC mode (deprecated but still supported)
example_legacy:
  TOP_NAME: retrosoc_asic
  RTL_FILE: ./minirv.sv
  SV_LIST: enable  # Legacy mode - generates retroSoC template filelist
  CLK_PORT_NAME: extclk_i_pad
  CLK_FREQ_MHZ: 100
  RESULT_DIR: ./results

# Example 4: No SystemVerilog files (traditional Verilog only)
example_verilog_only:
  TOP_NAME: my_design
  RTL_FILE: ./src/my_design.v
  # SV_LIST not specified - no SystemVerilog files
  CLK_PORT_NAME: clk
  CLK_FREQ_MHZ: 100
  RESULT_DIR: ./results

# Example filelist file content (sv_sources.f):
# +incdir+./src/includes
# ./src/interfaces/axi_if.sv
# ./src/interfaces/memory_if.sv
# ./src/modules/cpu_core.sv
# ./src/modules/cache_controller.sv
# ./src/packages/cpu_pkg.sv
