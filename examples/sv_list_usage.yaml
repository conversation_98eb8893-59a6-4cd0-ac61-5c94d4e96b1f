# Example configurations showing different SV_LIST usage patterns

# Example 1: Single SystemVerilog file
example_single_file:
  TOP_NAME: my_design
  RTL_FILE: ./src/my_design.v
  SV_LIST: ./src/my_design.sv  # Single SystemVerilog file
  CLK_PORT_NAME: clk
  CLK_FREQ_MHZ: 100
  RESULT_DIR: ./results

# Example 2: Multiple SystemVerilog files
example_multiple_files:
  TOP_NAME: my_design
  RTL_FILE: ./src/my_design.v
  SV_LIST:  # List of SystemVerilog files
    - ./src/module1.sv
    - ./src/module2.sv
    - ./src/interfaces.sv
  CLK_PORT_NAME: clk
  CLK_FREQ_MHZ: 100
  RESULT_DIR: ./results

# Example 3: Using a filelist file
example_filelist:
  TOP_NAME: my_design
  RTL_FILE: ./src/my_design.v
  SV_LIST: ./src/files.f  # Filelist containing SystemVerilog files
  CLK_PORT_NAME: clk
  CLK_FREQ_MHZ: 100
  RESULT_DIR: ./results

# Example 4: Legacy retroSoC mode (deprecated but still supported)
example_legacy:
  TOP_NAME: retrosoc_asic
  RTL_FILE: ./minirv.sv
  SV_LIST: enable  # Legacy mode - generates retroSoC template filelist
  CLK_PORT_NAME: extclk_i_pad
  CLK_FREQ_MHZ: 100
  RESULT_DIR: ./results

# Example 5: No SystemVerilog files (traditional Verilog only)
example_verilog_only:
  TOP_NAME: my_design
  RTL_FILE: ./src/my_design.v
  # SV_LIST not specified - no SystemVerilog files
  CLK_PORT_NAME: clk
  CLK_FREQ_MHZ: 100
  RESULT_DIR: ./results
