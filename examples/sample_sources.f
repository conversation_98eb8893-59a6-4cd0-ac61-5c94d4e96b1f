# Sample SystemVerilog filelist file
# This file demonstrates the proper format for SV_LIST parameter

# Include directories for SystemVerilog packages and headers
+incdir+./src/includes
+incdir+./src/packages

# SystemVerilog packages (should be compiled first)
./src/packages/cpu_pkg.sv
./src/packages/memory_pkg.sv
./src/packages/bus_pkg.sv

# Interface definitions
./src/interfaces/axi4_if.sv
./src/interfaces/memory_if.sv
./src/interfaces/debug_if.sv

# Core modules
./src/modules/cpu_core.sv
./src/modules/alu.sv
./src/modules/register_file.sv
./src/modules/control_unit.sv

# Memory subsystem
./src/modules/cache_controller.sv
./src/modules/memory_controller.sv
./src/modules/tlb.sv

# Peripheral modules
./src/modules/uart.sv
./src/modules/spi.sv
./src/modules/gpio.sv

# Top-level integration
./src/soc_top.sv
