create_grid -layer_name "MET1" -net_name_power VDD -net_name_ground VSS -width 0.16 -offset 0

create_stripe -layer_name "MET4"    -net_name_power VDD -net_name_ground VSS -width 1   -pitch 12 -offset 0.5
create_stripe -layer_name "MET5"    -net_name_power VDD -net_name_ground VSS -width 1   -pitch 12 -offset 1
create_stripe -layer_name "T4M2"    -net_name_power VDD -net_name_ground VSS -width 8   -pitch 20 -offset 4
create_stripe -layer_name "RDL"     -net_name_power VDD -net_name_ground VSS -width 35  -pitch 90 -offset 4

set connect1 "MET1 MET4"
set connect2 "MET4 MET5"
set connect3 "MET5 T4M2"
set connect4 "T4M2 RDL"

connect_two_layer -layers [concat $connect1 $connect2 $connect3 $connect4]

