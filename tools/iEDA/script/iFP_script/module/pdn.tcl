add_pdn_io -net_name VDD   -direction INOUT -is_power 1 
add_pdn_io -net_name VSS   -direction INOUT -is_power 0

global_net_connect  -net_name VDD  -instance_pin_name VDD    -is_power 1
global_net_connect  -net_name VDD  -instance_pin_name VDDPE  -is_power 1
global_net_connect  -net_name VDD  -instance_pin_name VDDCE  -is_power 1
global_net_connect  -net_name VDD  -instance_pin_name vdd    -is_power 1
global_net_connect  -net_name VSS  -instance_pin_name VSS    -is_power 0
global_net_connect  -net_name VSS  -instance_pin_name VSSE   -is_power 0
global_net_connect  -net_name VSS  -instance_pin_name vss    -is_power 0

create_grid -layer_name "MET1" -net_name_power VDD -net_name_ground VSS -width 0.16 -offset 0

create_stripe -layer_name "T4M2"    -net_name_power VDD -net_name_ground VSS -width 0.4 -pitch 20 -offset 0
create_stripe -layer_name "RDL" -net_name_power VDD -net_name_ground VSS -width 3 -pitch 20 -offset 0

set connect1 "MET1 T4M2"
set connect2 "T4M2 RDL"

connect_two_layer -layers [concat $connect1 $connect2]

