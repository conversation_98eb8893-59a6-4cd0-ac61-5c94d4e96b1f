#===========================================================

#===========================================================
set RESULT_DIR          "./ieda_results"

# input files
set INPUT_DEF           "$RESULT_DIR/iPL_filler_result.def"
set NUM_THREADS         64

# output files
set TOOL_REPORT_DIR     "$RESULT_DIR/report/drc/"

# script path
set IEDA_CONFIG_DIR     "$::env(IEDA_CONFIG_DIR)"
set IEDA_TCL_SCRIPT_DIR "$::env(IEDA_TCL_SCRIPT_DIR)"

#===========================================================
#   override variables from env setup
#===========================================================
source $IEDA_TCL_SCRIPT_DIR/DB_script/env_var_setup.tcl

#===========================================================
##   init flow config
#===========================================================
flow_init -config $IEDA_CONFIG_DIR/flow_config.json

#===========================================================
##   read db config
#===========================================================
db_init -config $IEDA_CONFIG_DIR/db_default_config.json -output_dir_path $RESULT_DIR

#===========================================================
##   reset data path
#===========================================================
source $IEDA_TCL_SCRIPT_DIR/DB_script/db_path_setting.tcl

#===========================================================
##   read lef
#===========================================================
source $IEDA_TCL_SCRIPT_DIR/DB_script/db_init_lef.tcl

#===========================================================
##   read def
#===========================================================
def_init -path $INPUT_DEF

#===========================================================
##   run drc
#===========================================================
init_drc -temp_directory_path "$TOOL_REPORT_DIR" \
        -thread_number $NUM_THREADS

check_def

destroy_drc

#===========================================================
##   Exit 
#===========================================================
flow_exit
