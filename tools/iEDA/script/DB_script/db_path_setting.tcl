if {[info exists ::env(SDC_FILE)]} {
    set SDC_FILE "$::env(SDC_FILE)"
}

if {[info exists ::env(SPEF_FILE)]} {
    set SPEF_PATH $::env(SPEF_FILE)
}

#===========================================================
##   set tech lef path
#===========================================================
# set TECH_LEF_PATH "$::env(FOUNDRY_DIR)/ihp-sg13g2/libs.ref/sg13g2_stdcell/lef/sg13g2_tech.lef"
set TECH_LEF_PATH "$::env(FOUNDRY_DIR)/lef/tlef_1P6M_7T_0530_zzs.lef"

#===========================================================
##   set lef path
#===========================================================                             
# set LEF_PATH "$::env(FOUNDRY_DIR)/ihp-sg13g2/libs.ref/sg13g2_stdcell/lef/sg13g2_stdcell.lef \
#               $::env(FOUNDRY_DIR)/ihp-sg13g2/libs.ref/sg13g2_io/lef/sg13g2_io.lef"

set LEF_PATH "$::env(FOUNDRY_DIR)/lef/ICSSCA_N55_H7BH.lef \
              $::env(FOUNDRY_DIR)/lef/ICSSCA_N55_H7BL.lef \
              $::env(FOUNDRY_DIR)/lef/ICSSCA_N55_H7BR.lef"

            #   $::env(FOUNDRY_DIR)/lef/ICSSCA_N55_H7BL_ant.lef"
            #   $::env(FOUNDRY_DIR)/lef/S55NLLG1PH_X256Y4D32_BW.lef \
            #   $::env(FOUNDRY_DIR)/lef/ICSIOA_N55_3P3_1P6M1TM.lef \
#===========================================================
##   set common lib path
#===========================================================
# set LIB_PATH "$::env(FOUNDRY_DIR)/ihp-sg13g2/libs.ref/sg13g2_stdcell/lib/sg13g2_stdcell_typ_1p20V_25C.lib \
#               $::env(FOUNDRY_DIR)/ihp-sg13g2/libs.ref/sg13g2_io/lib/sg13g2_io_typ_1p2V_3p3V_25C.lib"

set LIB_PATH "$::env(FOUNDRY_DIR)/lib/ETSCA_N55_H7BH_DSS_PRCMAX_V1P0_T125.lib \
              $::env(FOUNDRY_DIR)/lib/ETSCA_N55_H7BL_DSS_PRCMAX_V1P0_T125.lib \
              $::env(FOUNDRY_DIR)/lib/ETSCA_N55_H7BR_DSS_PRCMAX_V1P0_T125.lib"
            #   $::env(FOUNDRY_DIR)/lib/ETIOA_N55_3P3_ss1p08v2p97v125c.lib \
            #   $::env(FOUNDRY_DIR)/lib/S55NLLG1PH_X256Y4D32_BW_ss_1.08_125.lib"

#===========================================================
##   set fix fanout lib path
#===========================================================
set LIB_PATH_FIXFANOUT ${LIB_PATH}

#===========================================================
##   set drv lib path
#===========================================================
set LIB_PATH_DRV ${LIB_PATH}

#===========================================================
##   set hold lib path
#===========================================================
set LIB_PATH_HOLD ${LIB_PATH}

#===========================================================
##   set setup lib path
#===========================================================
set LIB_PATH_SETUP ${LIB_PATH}
