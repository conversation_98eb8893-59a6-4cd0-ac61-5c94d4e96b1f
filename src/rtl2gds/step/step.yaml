# rules for ${variables}:
# 1. case insensitive for each env key (will be translated into uppercase)
# 2. scopes for ${variables} start with R2G_ are global
# 3. scopes for ${variables} start with OUTPUT_ are only for [step_name][output_env][files]
# 4. every env(input_env/output_env/parameters) can be used as a variable in `cmd_template`
# 5. default_env and tool_env will be resolved at class init, rest env will be resolved at run time
# 6. Special reminder for step outputs:
#    - ${OUTPUT_PREFIX}, ${OUTPUT_STEP_NAME} and ${OUTPUT_TOP_NAME} are only used for output file names including ${dir_template},
#      will be constructed at `Step.process_output_files()`
#    - ${RESULT_DIR} will be resolved as ${RESULT_DIR}/${dir_template} at `Step.process_output_files()`
#    - the point is: ${RESULT_DIR} is the base directory for all step outputs, 
#      the final output directory for each step will be ${RESULT_DIR}/${dir_template}

# rule for special variables:
# `__optional__` : optional input file/parameter, skip check if not exist

default_env:
  FOUNDRY_DIR: ${R2G_PDK_DIR_ICS55}
  TECH_LEF: ${R2G_PDK_DIR_ICS55}/lib/ETSCA_N55_H7BL_DSS_PRCMAX_V1P0_T125.lib
  # CELL_GDS: ${R2G_PDK_DIR_ICS55}/ihp-sg13g2/libs.ref/sg13g2_stdcell/gds/sg13g2_stdcell.gds
  CELL_LEFS: ${R2G_PDK_DIR_ICS55}/lef/tlef_1P6M_7T_0530_zzs.lef
  RESULT_DIR: ${R2G_BASE_DIR}/rtl2gds_result

tool_env:
  iEDA:
    PATH: ${R2G_BIN_DIR}/iEDA
    LD_LIBRARY_PATH: ${R2G_BIN_DIR}/lib
    extra_env:
      IEDA_CONFIG_DIR: ${R2G_TOOL_DIR}/iEDA/iEDA_config
      IEDA_TCL_SCRIPT_DIR: ${R2G_TOOL_DIR}/iEDA/script
  magic:
    # use system path for magic
    extra_env:
      MAGIC_SCRIPTS_DIR: ${R2G_TOOL_DIR}/magic
  yosys:
    PATH: ${R2G_BIN_DIR}/yosys/bin
    LD_LIBRARY_PATH: ${R2G_BIN_DIR}/lib

sta:
  tool_name: iEDA
  cmd_template:
  - iEDA
  - ${R2G_TOOL_DIR}/iEDA/script/iSTA_script/report_timing_power.tcl
  input_env:
    files:
      INPUT_DEF: __optional__
      INPUT_VERILOG: __optional__
      SDC_FILE: ${R2G_TOOL_DIR}/default.sdc
    parameters:
      TOP_NAME: top
      USE_VERILOG: false
      CLK_PORT_NAME: clock
      CLK_FREQ_MHZ: 100
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      TOOL_REPORT_DIR: ${OUTPUT_TOP_NAME}_sta
      STA_SUMMARY_JSON: ${OUTPUT_TOP_NAME}_sta/${OUTPUT_TOP_NAME}.rpt.json
      POWER_SUMMARY_JSON: ${OUTPUT_TOP_NAME}_sta/${OUTPUT_TOP_NAME}.pwr.json
      POWER_INSTANCE_CSV: ${OUTPUT_TOP_NAME}_sta/${OUTPUT_TOP_NAME}_instance.csv
    metrics:
    - suggest_freq_mhz
    - setup_wns
    - setup_tns
    - hold_wns
    - hold_tns
    - internal_power
    - leakage_power
    - combinational_power
    - sequential_power
    - total_power

drc:
  tool_name: iEDA
  cmd_template:
  - iEDA
  - ${R2G_TOOL_DIR}/iEDA/script/iRT_script/run_iDRC.tcl
  input_env:
    files:
      INPUT_DEF: top.def
    parameters:
      TOP_NAME: top
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      TOOL_REPORT_DIR: drc_report
      DRC_REPORT_JSON: drc_report/violation_map.json
  metrics:
    - num_drc_violations

drc_magic:
  tool_name: magic
  cmd_template:
  - magic
  - -noconsole
  - -dnull
  - -rcfile
  - ${R2G_PDK_DIR_IHP130}/ihp-sg13g2/libs.tech/magic/ihp-sg13g2.magicrc
  - ${R2G_TOOL_DIR}/magic/drc.tcl
  - ${TOP_NAME}
  - ${GDS_FILE}
  - ${DRC_REPORT_JSON}
  input_env:
    files:
      GDS_FILE: top.gds
    parameters:
      TOP_NAME: top
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      DRC_REPORT_JSON: ${OUTPUT_TOP_NAME}_drc.json
    metrics:
    - num_drc_violations

# gds:
#   tool_name: magic
#   cmd_template:
#   - magic
#   - -noconsole
#   - -dnull
#   - -rcfile
#   - ${R2G_PDK_DIR_IHP130}/ihp-sg13g2/libs.tech/magic/ihp-sg13g2.magicrc
#   - ${R2G_TOOL_DIR}/magic/gds.tcl
#   - ${TOP_NAME}
#   - ${DIE_BBOX}
#   - ${INPUT_DEF}
#   - ./
#   - ${GDS_FILE}
#   input_env:
#     files:
#       INPUT_DEF: top.def
#     parameters:
#       TOP_NAME: top
#       DIE_BBOX:
#   output_env:
#     dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
#     files:
#       GDS_FILE: ${OUTPUT_TOP_NAME}.gds
#     metrics:
#     - elapsed_time
#     - peak_memory_mb

layout_json_fp:
  tool_name: iEDA
  cmd_template:
  - iEDA
  - ${R2G_TOOL_DIR}/iEDA/script/DB_script/run_def_to_json_fp.tcl
  input_env:
    files:
      INPUT_DEF: top_floorplan.def
    parameters:
      JSON_STAGE: fp
      TOP_NAME: top
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      LAYOUT_JSON_FILE: ${OUTPUT_TOP_NAME}_floorplan.json
    metrics:
    - elapsed_time
    - peak_memory_mb

synthesis:
  tool_name: yosys
  cmd_template:
  - yosys
  - ${R2G_TOOL_DIR}/yosys/scripts/yosys_synthesis.tcl
  input_env:
    files:
      RTL_FILE: top.v
      FILELIST: __optional__
    parameters:
      SV_LIST: __optional__
      TOP_NAME: top
      CLK_FREQ_MHZ: 100
      KEEP_HIERARCHY: false
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      NETLIST_FILE: ${OUTPUT_TOP_NAME}_nl.v
      TIMING_CELL_STAT_RPT: ${OUTPUT_TOP_NAME}_timing_cells.rpt
      TIMING_CELL_COUNT_RPT: ${OUTPUT_TOP_NAME}_timing_cell_count.rpt
      GENERIC_STAT_JSON: ${OUTPUT_TOP_NAME}_generic_stat.json
      SYNTH_STAT_JSON: ${OUTPUT_TOP_NAME}_synth_stat.json
      SYNTH_CHECK_RPT: ${OUTPUT_TOP_NAME}_synth_check.rpt
      RUNTIME_LOG: subprocess_runtime.log
    metrics:
    - num_cells
    - cell_area
    - num_posedge_reg
    - num_negedge_reg
    - num_latch

floorplan:
  tool_name: iEDA
  cmd_template:
  - iEDA
  - ${R2G_TOOL_DIR}/iEDA/script/iFP_script/run_iFP.tcl
  input_env:
    files:
      NETLIST_FILE: top_nl.v
    parameters:
      TOP_NAME: top
      CORE_UTIL: 0.5
      CELL_AREA: __optional__
      USE_FIXED_BBOX: true
      DIE_BBOX: __optional__
      CORE_BBOX: __optional__
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      OUTPUT_DEF: ${OUTPUT_TOP_NAME}_floorplan.def
      OUTPUT_VERILOG: ${OUTPUT_TOP_NAME}_floorplan.v
      DESIGN_STAT_TEXT: ${OUTPUT_TOP_NAME}_floorplan_stat.rpt
      DESIGN_STAT_JSON: ${OUTPUT_TOP_NAME}_floorplan_stat.json
      RUNTIME_LOG: subprocess_runtime.log
    metrics:
    - die_bbox
    - core_bbox
    - core_util
    - num_cells
    - cell_area

netlist_opt:
  tool_name: iEDA
  cmd_template:
  - iEDA
  - ${R2G_TOOL_DIR}/iEDA/script/iNO_script/run_iNO_fix_fanout.tcl
  input_env:
    files:
      INPUT_DEF: top_floorplan.def
    parameters:
      TOP_NAME: top
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      OUTPUT_DEF: ${OUTPUT_TOP_NAME}_netlist_opt.def
      OUTPUT_VERILOG: ${OUTPUT_TOP_NAME}_netlist_opt.v
      DESIGN_STAT_TEXT: ${OUTPUT_TOP_NAME}_netlist_opt_stat.rpt
      DESIGN_STAT_JSON: ${OUTPUT_TOP_NAME}_netlist_opt_stat.json
      TOOL_METRICS_JSON: ${OUTPUT_TOP_NAME}_netlist_opt_metrics.json
    metrics:
    - num_cells
    - cell_area

placement:
  tool_name: iEDA
  cmd_template:
  - iEDA
  - ${R2G_TOOL_DIR}/iEDA/script/iPL_script/run_iPL.tcl
  input_env:
    files:
      INPUT_DEF: top_netlist_opt.def
    parameters:
      TOP_NAME: top
      DENSITY_GRID_SIZE: 1
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      OUTPUT_DEF: ${OUTPUT_TOP_NAME}_placement.def
      OUTPUT_VERILOG: ${OUTPUT_TOP_NAME}_placement.v
      DESIGN_STAT_TEXT: ${OUTPUT_TOP_NAME}_placement_stat.rpt
      DESIGN_STAT_JSON: ${OUTPUT_TOP_NAME}_placement_stat.json
      TOOL_METRICS_JSON: ${OUTPUT_TOP_NAME}_placement_metrics.json
      TOOL_REPORT_DIR: ./
      DESIGN_EVAL_REPORT: ${OUTPUT_TOP_NAME}_eval
      # CONGESTION_MAP: rt/place_egr_union_overflow.csv
      DENSITY_MAP_CELL: density_map/legalization_allcell_density.csv
      DENSITY_MAP_PIN: density_map/legalization_allcell_pin_density.csv
      DENSITY_MAP_NET: density_map/legalization_allnet_density.csv
      RUNTIME_LOG: subprocess_runtime.log
    metrics:
    - num_cells
    - cell_area

placement_aieda:
  tool_name: iEDA
  cmd_template:
  - python3
  - ${R2G_TOOL_DIR}/AiEDA/flow/run_single_step.py
  - --step
  - place
  - -t
  - AIMP
  - --input-def
  - ${INPUT_DEF}
  - --input-verilog
  - ${INPUT_VERILOG}
  - --output-def
  - ${OUTPUT_DEF}
  - --output-verilog
  - ${OUTPUT_VERILOG}
  - --workspace
  - ${WORKSPACE}
  input_env:
    files:
      INPUT_DEF: top_netlist_opt.def
      INPUT_VERILOG: top_netlist_opt.v
      WORKSPACE: ${R2G_TOOL_DIR}/AiEDA/init_workspace
    parameters:
      TOP_NAME: top
      DENSITY_GRID_SIZE: 1
      eda_tool: iEDA
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      OUTPUT_DEF: ${OUTPUT_TOP_NAME}_placement.def
      OUTPUT_VERILOG: ${OUTPUT_TOP_NAME}_placement.v
      # DESIGN_STAT_TEXT: ${OUTPUT_TOP_NAME}_placement_stat.rpt
      # DESIGN_STAT_JSON: ${OUTPUT_TOP_NAME}_placement_stat.json
      # TOOL_METRICS_JSON: ${OUTPUT_TOP_NAME}_placement_metrics.json
      TOOL_REPORT_DIR: ./
      # DESIGN_EVAL_REPORT: ${OUTPUT_TOP_NAME}_eval
      # CONGESTION_MAP: rt/place_egr_union_overflow.csv
      # INSTANCE_PLOTS: pl/plot
    metrics:
    - num_cells
    - cell_area

cts:
  tool_name: iEDA
  cmd_template:
  - iEDA
  - ${R2G_TOOL_DIR}/iEDA/script/iCTS_script/run_iCTS.tcl
  input_env:
    files:
      INPUT_DEF: top_placement.def
    parameters:
      TOP_NAME: top
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      OUTPUT_DEF: ${OUTPUT_TOP_NAME}_cts.def
      OUTPUT_VERILOG: ${OUTPUT_TOP_NAME}_cts.v
      DESIGN_STAT_TEXT: ${OUTPUT_TOP_NAME}_cts_stat.rpt
      DESIGN_STAT_JSON: ${OUTPUT_TOP_NAME}_cts_stat.json
      TOOL_METRICS_JSON: ${OUTPUT_TOP_NAME}_cts_metrics.json
      TOOL_REPORT_DIR: cts
      CLOCK_TREE_JSON: cts/output/cts_design.json
      RUNTIME_LOG: subprocess_runtime.log
    metrics:
    - num_cells
    - cell_area

legalization:
  tool_name: iEDA
  cmd_template:
  - iEDA
  - ${R2G_TOOL_DIR}/iEDA/script/iPL_script/run_iPL_legalization.tcl
  input_env:
    files:
      INPUT_DEF: top_cts.def
    parameters:
      TOP_NAME: top
      DESIGN_EVAL_REPORT: ${RESULT_DIR}/evaluation/legalization
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      OUTPUT_DEF: ${OUTPUT_TOP_NAME}_legalization.def
      OUTPUT_VERILOG: ${OUTPUT_TOP_NAME}_legalization.v
      DESIGN_STAT_TEXT: ${OUTPUT_TOP_NAME}_legalization_stat.rpt
      DESIGN_STAT_JSON: ${OUTPUT_TOP_NAME}_legalization_stat.json
    metrics:
    - num_cells
    - cell_area

routing:
  tool_name: iEDA
  cmd_template:
  - iEDA
  - ${R2G_TOOL_DIR}/iEDA/script/iRT_script/run_iRT.tcl
  input_env:
    files:
      INPUT_DEF: top_legalization.def
    parameters:
      TOP_NAME: top
      FAST_ROUTE: true
      # IEDA_ECOS_NOTIFICATION_URL: "@TODO"
      # IEDA_ECOS_NOTIFICATION_SECRET: "@TODO"
      # ECOS_TASK_ID: "@TODO"
      # ECOS_PROJECT_ID: "@TODO"
      # ECOS_TASK_TYPE: "@TODO"
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      OUTPUT_DEF: ${OUTPUT_TOP_NAME}_routing.def
      OUTPUT_VERILOG: ${OUTPUT_TOP_NAME}_routing.v
      DESIGN_STAT_TEXT: ${OUTPUT_TOP_NAME}_routing_stat.rpt
      DESIGN_STAT_JSON: ${OUTPUT_TOP_NAME}_routing_stat.json
      TOOL_METRICS_JSON: ${OUTPUT_TOP_NAME}_routing_metrics.json
      TOOL_REPORT_DIR: routing
      RUNTIME_LOG: subprocess_runtime.log
    metrics:
    - num_cells
    - cell_area

filler:
  tool_name: iEDA
  cmd_template:
  - iEDA
  - ${R2G_TOOL_DIR}/iEDA/script/iPL_script/run_iPL_filler.tcl
  input_env:
    files:
      INPUT_DEF: top_routing.def
    parameters:
      TOP_NAME: top
  output_env:
    dir_template: ${OUTPUT_PREFIX}_${OUTPUT_STEP_NAME}_${OUTPUT_TOP_NAME}
    files:
      OUTPUT_DEF: ${OUTPUT_TOP_NAME}_filler.def
      OUTPUT_VERILOG: ${OUTPUT_TOP_NAME}_filler.v
      DESIGN_STAT_TEXT: ${OUTPUT_TOP_NAME}_filler_stat.rpt
      DESIGN_STAT_JSON: ${OUTPUT_TOP_NAME}_filler_stat.json
    metrics:
    - num_cells
    - cell_area
