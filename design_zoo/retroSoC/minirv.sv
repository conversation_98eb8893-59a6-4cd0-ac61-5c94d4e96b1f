// Generated by CIRCT firtool-1.66.0
// Standard header to adapt well known macros for prints and assertions.
`define SYNTHESIS
// Users can define 'ASSERT_VERBOSE_COND' to add an extra gate to assert error printing.
// Standard header to adapt well known macros for register randomization.
`ifndef RANDOMIZE
  `ifdef RANDOMIZE_REG_INIT
    `define RANDOMIZ<PERSON>
  `endif // RANDOMIZE_REG_INIT
`endif // not def RANDOMIZE

// RANDOM may be set to an expression that produces a 32-bit random unsigned value.
`ifndef RANDOM
  `define RANDOM $random
`endif // not def RANDOM

// Users can define INIT_RANDOM as general code that gets injected into the
// initializer block for modules with registers.
`ifndef INIT_RANDOM
  `define INIT_RANDOM
`endif // not def INIT_RANDOM

// If using random initialization, you can also define RANDOMIZE_DELAY to
// customize the delay used, otherwise 0.002 is used.
`ifndef RANDOMIZE_DELAY
  `define RANDOMIZE_DELAY 0.002
`endif // not def RANDOMIZE_DELAY

// Define INIT_RANDOM_PROLOG_ for use in our modules below.
`ifndef INIT_RANDOM_PROLOG_
  `ifdef RANDOMIZE
    `ifdef VERILATOR
      `define INIT_RANDOM_PROLOG_ `INIT_RANDOM
    `else  // VERILATOR
      `define INIT_RANDOM_PROLOG_ `INIT_RANDOM #`RANDOMIZE_DELAY begin end
    `endif // VERILATOR
  `else  // RANDOMIZE
    `define INIT_RANDOM_PROLOG_
  `endif // RANDOMIZE
`endif // not def INIT_RANDOM_PROLOG_

// Include register initializers in init blocks unless synthesis is set
`ifndef SYNTHESIS
  `ifndef ENABLE_INITIAL_REG_
    `define ENABLE_INITIAL_REG_
  `endif // not def ENABLE_INITIAL_REG_
`endif // not def SYNTHESIS

// Include rmemory initializers in init blocks unless synthesis is set
`ifndef SYNTHESIS
  `ifndef ENABLE_INITIAL_MEM_
    `define ENABLE_INITIAL_MEM_
  `endif // not def ENABLE_INITIAL_MEM_
`endif // not def SYNTHESIS

`ifndef ASSERT_VERBOSE_COND_
  `ifdef ASSERT_VERBOSE_COND
    `define ASSERT_VERBOSE_COND_ (`ASSERT_VERBOSE_COND)
  `else  // ASSERT_VERBOSE_COND
    `define ASSERT_VERBOSE_COND_ 1
  `endif // ASSERT_VERBOSE_COND
`endif // not def ASSERT_VERBOSE_COND_

// Users can define 'STOP_COND' to add an extra gate to stop conditions.
`ifndef STOP_COND_
  `ifdef STOP_COND
    `define STOP_COND_ (`STOP_COND)
  `else  // STOP_COND
    `define STOP_COND_ 1
  `endif // STOP_COND
`endif // not def STOP_COND_

module IFU(	// @[src/main/IFU.scala:10:7]
  input         clock,	// @[src/main/IFU.scala:10:7]
                reset,	// @[src/main/IFU.scala:10:7]
  output [31:0] io_mem_pc,	// @[src/main/IFU.scala:11:14]
  output        io_mem_pcValid,	// @[src/main/IFU.scala:11:14]
  input  [31:0] io_mem_inst,	// @[src/main/IFU.scala:11:14]
  input         io_mem_instValid,	// @[src/main/IFU.scala:11:14]
  output        io_out_valid,	// @[src/main/IFU.scala:11:14]
  output [31:0] io_out_bits_pc,	// @[src/main/IFU.scala:11:14]
                io_out_bits_inst,	// @[src/main/IFU.scala:11:14]
  input         io_redirect_valid,	// @[src/main/IFU.scala:11:14]
  input  [31:0] io_redirect_dnpc,	// @[src/main/IFU.scala:11:14]
  input         io_nextInst	// @[src/main/IFU.scala:11:14]
);

  reg  [31:0] PC;	// @[src/main/IFU.scala:18:19]
  reg         fetchInst_REG;	// @[src/main/IFU.scala:22:49]
  reg         fetchInst;	// @[src/main/IFU.scala:22:26]
  reg         state;	// @[src/main/IFU.scala:28:22]
  wire [31:0] _PC_T = PC + 32'h4;	// @[src/main/IFU.scala:18:19, :20:60]
  always @(posedge clock) begin	// @[src/main/IFU.scala:10:7]
    if (reset) begin	// @[src/main/IFU.scala:10:7]
      PC <= 32'h30000000;	// @[src/main/IFU.scala:18:19]
      fetchInst_REG <= 1'h1;	// @[src/main/IFU.scala:10:7, :22:49]
      fetchInst <= 1'h0;	// @[src/main/IFU.scala:10:7, :22:26]
      state <= 1'h0;	// @[src/main/IFU.scala:10:7, :28:22]
    end
    else begin	// @[src/main/IFU.scala:10:7]
      if (io_nextInst)	// @[src/main/IFU.scala:11:14]
        PC <= {io_redirect_valid ? io_redirect_dnpc[31:2] : _PC_T[31:2], 2'h0};	// @[src/main/IFU.scala:7:40, :10:7, :11:14, :18:19, :20:{19,60}]
      fetchInst_REG <= 1'h0;	// @[src/main/IFU.scala:10:7, :22:49]
      fetchInst <= io_nextInst | fetchInst_REG;	// @[src/main/IFU.scala:22:{26,39,49}]
      state <= ~state & fetchInst | state & ~io_mem_instValid;	// @[src/main/IFU.scala:22:26, :28:22, :31:33, src/main/scala/chisel3/util/Mux.scala:30:73, src/main/utils/Utils.scala:21:92]
    end
  end // always @(posedge)
  `ifdef ENABLE_INITIAL_REG_	// @[src/main/IFU.scala:10:7]
    `ifdef FIRRTL_BEFORE_INITIAL	// @[src/main/IFU.scala:10:7]
      `FIRRTL_BEFORE_INITIAL	// @[src/main/IFU.scala:10:7]
    `endif // FIRRTL_BEFORE_INITIAL
    logic [31:0] _RANDOM[0:1];	// @[src/main/IFU.scala:10:7]
    initial begin	// @[src/main/IFU.scala:10:7]
      `ifdef INIT_RANDOM_PROLOG_	// @[src/main/IFU.scala:10:7]
        `INIT_RANDOM_PROLOG_	// @[src/main/IFU.scala:10:7]
      `endif // INIT_RANDOM_PROLOG_
      `ifdef RANDOMIZE_REG_INIT	// @[src/main/IFU.scala:10:7]
        for (logic [1:0] i = 2'h0; i < 2'h2; i += 2'h1) begin
          _RANDOM[i[0]] = `RANDOM;	// @[src/main/IFU.scala:10:7]
        end	// @[src/main/IFU.scala:10:7]
        PC = _RANDOM[1'h0];	// @[src/main/IFU.scala:10:7, :18:19]
        fetchInst_REG = _RANDOM[1'h1][0];	// @[src/main/IFU.scala:10:7, :22:49]
        fetchInst = _RANDOM[1'h1][1];	// @[src/main/IFU.scala:10:7, :22:{26,49}]
        state = _RANDOM[1'h1][2];	// @[src/main/IFU.scala:10:7, :22:49, :28:22]
      `endif // RANDOMIZE_REG_INIT
    end // initial
    `ifdef FIRRTL_AFTER_INITIAL	// @[src/main/IFU.scala:10:7]
      `FIRRTL_AFTER_INITIAL	// @[src/main/IFU.scala:10:7]
    `endif // FIRRTL_AFTER_INITIAL
  `endif // ENABLE_INITIAL_REG_
  assign io_mem_pc = {PC[31:2], 2'h0};	// @[src/main/IFU.scala:7:{25,40}, :10:7, :18:19]
  assign io_mem_pcValid = fetchInst & ~state;	// @[src/main/IFU.scala:10:7, :22:26, :28:22, :34:31, src/main/utils/Utils.scala:21:92]
  assign io_out_valid = io_mem_instValid;	// @[src/main/IFU.scala:10:7]
  assign io_out_bits_pc = {PC[31:2], 2'h0};	// @[src/main/IFU.scala:7:{25,40}, :10:7, :18:19]
  assign io_out_bits_inst = io_mem_inst;	// @[src/main/IFU.scala:10:7]
endmodule

module IDU(	// @[src/main/IDU.scala:6:7]
  input         clock,	// @[src/main/IDU.scala:6:7]
                io_in_valid,	// @[src/main/IDU.scala:7:14]
  input  [31:0] io_in_bits_pc,	// @[src/main/IDU.scala:7:14]
                io_in_bits_inst,	// @[src/main/IDU.scala:7:14]
  output        io_out_valid,	// @[src/main/IDU.scala:7:14]
  output [31:0] io_out_bits_src1,	// @[src/main/IDU.scala:7:14]
                io_out_bits_src2,	// @[src/main/IDU.scala:7:14]
                io_out_bits_src3,	// @[src/main/IDU.scala:7:14]
  output        io_out_bits_wen,	// @[src/main/IDU.scala:7:14]
  output [3:0]  io_out_bits_rd,	// @[src/main/IDU.scala:7:14]
  output [2:0]  io_out_bits_decode_funct3,	// @[src/main/IDU.scala:7:14]
  output        io_out_bits_decode_isLoad,	// @[src/main/IDU.scala:7:14]
                io_out_bits_decode_isStore,	// @[src/main/IDU.scala:7:14]
                io_out_bits_decode_isJmp,	// @[src/main/IDU.scala:7:14]
                io_out_bits_decode_isCSR,	// @[src/main/IDU.scala:7:14]
  input  [31:0] io_wb_res,	// @[src/main/IDU.scala:7:14]
  input         io_wb_wen,	// @[src/main/IDU.scala:7:14]
  input  [3:0]  io_wb_rd	// @[src/main/IDU.scala:7:14]
);

  wire        isUtype = io_in_bits_inst[4:2] == 3'h5;	// @[src/main/IDU.scala:18:{21,28}]
  wire        isStype = io_in_bits_inst[6:2] == 5'h8;	// @[src/main/IDU.scala:19:{21,28}]
  wire        isRtype = io_in_bits_inst[6:2] == 5'hC | io_in_bits_inst[6:2] == 5'hE;	// @[src/main/IDU.scala:19:21, :21:{29,45,60}]
  wire        isJalr = io_in_bits_inst[6:2] == 5'h19;	// @[src/main/IDU.scala:19:21, :27:27]
  reg  [31:0] R_1;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_2;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_3;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_4;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_5;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_6;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_7;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_8;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_9;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_10;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_11;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_12;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_13;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_14;	// @[src/main/IDU.scala:49:59]
  reg  [31:0] R_15;	// @[src/main/IDU.scala:49:59]
  wire [31:0] rs2Val =
    R_1 & {32{io_in_bits_inst[24:20] == 5'h1}} | R_2
    & {32{io_in_bits_inst[24:20] == 5'h2}} | R_3 & {32{io_in_bits_inst[24:20] == 5'h3}}
    | R_4 & {32{io_in_bits_inst[24:20] == 5'h4}} | R_5
    & {32{io_in_bits_inst[24:20] == 5'h5}} | R_6 & {32{io_in_bits_inst[24:20] == 5'h6}}
    | R_7 & {32{io_in_bits_inst[24:20] == 5'h7}} | R_8
    & {32{io_in_bits_inst[24:20] == 5'h8}} | R_9 & {32{io_in_bits_inst[24:20] == 5'h9}}
    | R_10 & {32{io_in_bits_inst[24:20] == 5'hA}} | R_11
    & {32{io_in_bits_inst[24:20] == 5'hB}} | R_12 & {32{io_in_bits_inst[24:20] == 5'hC}}
    | R_13 & {32{io_in_bits_inst[24:20] == 5'hD}} | R_14
    & {32{io_in_bits_inst[24:20] == 5'hE}} | R_15 & {32{io_in_bits_inst[24:20] == 5'hF}};	// @[src/main/IDU.scala:41:37, :49:59, :50:{66,72,84,107}]
  always @(posedge clock) begin	// @[src/main/IDU.scala:6:7]
    if (io_wb_wen & io_wb_rd == 4'h1)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_1 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'h2)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_2 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'h3)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_3 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'h4)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_4 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'h5)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_5 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'h6)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_6 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'h7)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_7 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'h8)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_8 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'h9)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_9 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'hA)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_10 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'hB)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_11 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'hC)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_12 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'hD)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_13 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & io_wb_rd == 4'hE)	// @[src/main/IDU.scala:6:7, :49:59, :51:{98,107,111}, :72:20]
      R_14 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
    if (io_wb_wen & (&io_wb_rd))	// @[src/main/IDU.scala:49:59, :51:{98,107,111}, :72:20]
      R_15 <= io_wb_res;	// @[src/main/IDU.scala:49:59]
  end // always @(posedge)
  `ifdef ENABLE_INITIAL_REG_	// @[src/main/IDU.scala:6:7]
    `ifdef FIRRTL_BEFORE_INITIAL	// @[src/main/IDU.scala:6:7]
      `FIRRTL_BEFORE_INITIAL	// @[src/main/IDU.scala:6:7]
    `endif // FIRRTL_BEFORE_INITIAL
    logic [31:0] _RANDOM[0:14];	// @[src/main/IDU.scala:6:7]
    initial begin	// @[src/main/IDU.scala:6:7]
      `ifdef INIT_RANDOM_PROLOG_	// @[src/main/IDU.scala:6:7]
        `INIT_RANDOM_PROLOG_	// @[src/main/IDU.scala:6:7]
      `endif // INIT_RANDOM_PROLOG_
      `ifdef RANDOMIZE_REG_INIT	// @[src/main/IDU.scala:6:7]
        for (logic [3:0] i = 4'h0; i < 4'hF; i += 4'h1) begin
          _RANDOM[i] = `RANDOM;	// @[src/main/IDU.scala:6:7]
        end	// @[src/main/IDU.scala:6:7]
        R_1 = _RANDOM[4'h0];	// @[src/main/IDU.scala:6:7, :49:59]
        R_2 = _RANDOM[4'h1];	// @[src/main/IDU.scala:6:7, :49:59]
        R_3 = _RANDOM[4'h2];	// @[src/main/IDU.scala:6:7, :49:59]
        R_4 = _RANDOM[4'h3];	// @[src/main/IDU.scala:6:7, :49:59]
        R_5 = _RANDOM[4'h4];	// @[src/main/IDU.scala:6:7, :49:59]
        R_6 = _RANDOM[4'h5];	// @[src/main/IDU.scala:6:7, :49:59]
        R_7 = _RANDOM[4'h6];	// @[src/main/IDU.scala:6:7, :49:59]
        R_8 = _RANDOM[4'h7];	// @[src/main/IDU.scala:6:7, :49:59]
        R_9 = _RANDOM[4'h8];	// @[src/main/IDU.scala:6:7, :49:59]
        R_10 = _RANDOM[4'h9];	// @[src/main/IDU.scala:6:7, :49:59]
        R_11 = _RANDOM[4'hA];	// @[src/main/IDU.scala:6:7, :49:59]
        R_12 = _RANDOM[4'hB];	// @[src/main/IDU.scala:6:7, :49:59]
        R_13 = _RANDOM[4'hC];	// @[src/main/IDU.scala:6:7, :49:59]
        R_14 = _RANDOM[4'hD];	// @[src/main/IDU.scala:6:7, :49:59]
        R_15 = _RANDOM[4'hE];	// @[src/main/IDU.scala:6:7, :49:59]
      `endif // RANDOMIZE_REG_INIT
    end // initial
    `ifdef FIRRTL_AFTER_INITIAL	// @[src/main/IDU.scala:6:7]
      `FIRRTL_AFTER_INITIAL	// @[src/main/IDU.scala:6:7]
    `endif // FIRRTL_AFTER_INITIAL
  `endif // ENABLE_INITIAL_REG_
  assign io_out_valid = io_in_valid;	// @[src/main/IDU.scala:6:7]
  assign io_out_bits_src1 =
    isUtype
      ? (io_in_bits_inst[6:2] == 5'hD ? 32'h0 : {io_in_bits_pc[31:2], 2'h0})
      : R_1 & {32{io_in_bits_inst[19:15] == 5'h1}} | R_2
        & {32{io_in_bits_inst[19:15] == 5'h2}} | R_3
        & {32{io_in_bits_inst[19:15] == 5'h3}} | R_4
        & {32{io_in_bits_inst[19:15] == 5'h4}} | R_5
        & {32{io_in_bits_inst[19:15] == 5'h5}} | R_6
        & {32{io_in_bits_inst[19:15] == 5'h6}} | R_7
        & {32{io_in_bits_inst[19:15] == 5'h7}} | R_8
        & {32{io_in_bits_inst[19:15] == 5'h8}} | R_9
        & {32{io_in_bits_inst[19:15] == 5'h9}} | R_10
        & {32{io_in_bits_inst[19:15] == 5'hA}} | R_11
        & {32{io_in_bits_inst[19:15] == 5'hB}} | R_12
        & {32{io_in_bits_inst[19:15] == 5'hC}} | R_13
        & {32{io_in_bits_inst[19:15] == 5'hD}} | R_14
        & {32{io_in_bits_inst[19:15] == 5'hE}} | R_15
        & {32{io_in_bits_inst[19:15] == 5'hF}};	// @[src/main/IDU.scala:6:7, :18:28, :19:21, :24:27, :40:37, :49:59, :50:{66,72,84,107}, :62:{18,48}, src/main/IFU.scala:7:{25,40}]
  assign io_out_bits_src2 =
    (isRtype ? rs2Val : 32'h0)
    | (isStype
         ? {{20{io_in_bits_inst[31]}}, io_in_bits_inst[31:25], io_in_bits_inst[11:7]}
         : 32'h0) | (isUtype ? {io_in_bits_inst[31:12], 12'h0} : 32'h0)
    | (isRtype | isStype | isUtype
         ? 32'h0
         : {{20{io_in_bits_inst[31]}}, io_in_bits_inst[31:20]});	// @[src/main/IDU.scala:6:7, :18:28, :19:28, :21:45, :35:26, :36:{25,30}, :37:{30,44}, :50:107, src/main/scala/chisel3/util/Mux.scala:30:73, src/main/utils/Utils.scala:8:{33,38}, :13:47]
  assign io_out_bits_src3 =
    isStype ? rs2Val : {io_in_bits_pc[31:2], 2'h0} + {29'h0, isJalr, 2'h0};	// @[src/main/IDU.scala:6:7, :19:28, :27:27, :50:107, :68:{18,39}, src/main/IFU.scala:7:25]
  assign io_out_bits_wen = ~isStype;	// @[src/main/IDU.scala:6:7, :19:28, :42:14]
  assign io_out_bits_rd = io_in_bits_inst[10:7];	// @[src/main/IDU.scala:6:7, :37:44, :39:10]
  assign io_out_bits_decode_funct3 = io_in_bits_inst[14:12];	// @[src/main/IDU.scala:6:7, :23:20]
  assign io_out_bits_decode_isLoad = io_in_bits_inst[6:2] == 5'h0;	// @[src/main/IDU.scala:6:7, :19:21, :26:27]
  assign io_out_bits_decode_isStore = isStype;	// @[src/main/IDU.scala:6:7, :19:28]
  assign io_out_bits_decode_isJmp = isJalr;	// @[src/main/IDU.scala:6:7, :27:27]
  assign io_out_bits_decode_isCSR =
    io_in_bits_inst[6:2] == 5'h1C & (|(io_in_bits_inst[14:12]));	// @[src/main/IDU.scala:6:7, :19:21, :23:20, :25:{28,44,55}]
endmodule

module EXU(	// @[src/main/EXU.scala:6:7]
  input         io_in_valid,	// @[src/main/EXU.scala:7:14]
  input  [31:0] io_in_bits_src1,	// @[src/main/EXU.scala:7:14]
                io_in_bits_src2,	// @[src/main/EXU.scala:7:14]
                io_in_bits_src3,	// @[src/main/EXU.scala:7:14]
  input         io_in_bits_wen,	// @[src/main/EXU.scala:7:14]
  input  [3:0]  io_in_bits_rd,	// @[src/main/EXU.scala:7:14]
  input  [2:0]  io_in_bits_decode_funct3,	// @[src/main/EXU.scala:7:14]
  input         io_in_bits_decode_isLoad,	// @[src/main/EXU.scala:7:14]
                io_in_bits_decode_isStore,	// @[src/main/EXU.scala:7:14]
                io_in_bits_decode_isJmp,	// @[src/main/EXU.scala:7:14]
                io_in_bits_decode_isCSR,	// @[src/main/EXU.scala:7:14]
  output        io_out_valid,	// @[src/main/EXU.scala:7:14]
  output [31:0] io_out_bits_res,	// @[src/main/EXU.scala:7:14]
  output        io_out_bits_wen,	// @[src/main/EXU.scala:7:14]
  output [3:0]  io_out_bits_rd,	// @[src/main/EXU.scala:7:14]
  output [31:0] io_out_bits_wdata,	// @[src/main/EXU.scala:7:14]
  output [3:0]  io_out_bits_wmask,	// @[src/main/EXU.scala:7:14]
  output [2:0]  io_out_bits_decode_funct3,	// @[src/main/EXU.scala:7:14]
  output        io_out_bits_decode_isLoad,	// @[src/main/EXU.scala:7:14]
                io_out_bits_decode_isStore,	// @[src/main/EXU.scala:7:14]
                io_redirect_valid,	// @[src/main/EXU.scala:7:14]
  output [31:0] io_redirect_dnpc	// @[src/main/EXU.scala:7:14]
);

  wire [31:0] _sum_T = io_in_bits_src1 + io_in_bits_src2;	// @[src/main/EXU.scala:17:21]
  reg  [7:0]  casez_tmp;	// @[src/main/scala/chisel3/util/Mux.scala:30:73]
  always_comb begin
    casez ({1'h0, _sum_T[1:0]})	// @[src/main/EXU.scala:17:21, :22:111, src/main/scala/chisel3/util/Mux.scala:30:73]
      3'b000:
        casez_tmp = 8'h1;
      3'b001:
        casez_tmp = 8'h2;
      3'b010:
        casez_tmp = 8'h4;
      3'b011:
        casez_tmp = 8'h8;
      3'b100:
        casez_tmp = 8'h10;
      3'b101:
        casez_tmp = 8'h20;
      3'b110:
        casez_tmp = 8'h40;
      default:
        casez_tmp = 8'h80;
    endcase	// @[src/main/EXU.scala:17:21, :22:111, src/main/scala/chisel3/util/Mux.scala:30:73]
  end // always_comb
  assign io_out_valid = io_in_valid;	// @[src/main/EXU.scala:6:7]
  assign io_out_bits_res =
    (io_in_bits_decode_isJmp ? io_in_bits_src3 : 32'h0)
    | (io_in_bits_decode_isCSR
         ? (io_in_bits_src2[11:0] == 12'hF11 ? 32'h79737978 : 32'h0)
           | (io_in_bits_src2[11:0] == 12'hF12 ? 32'h12211D6 : 32'h0)
         : 32'h0) | (io_in_bits_decode_isJmp | io_in_bits_decode_isCSR ? 32'h0 : _sum_T);	// @[src/main/EXU.scala:6:7, :17:21, :33:24, src/main/scala/chisel3/util/Mux.scala:30:73, src/main/utils/Utils.scala:21:92]
  assign io_out_bits_wen = io_in_bits_wen;	// @[src/main/EXU.scala:6:7]
  assign io_out_bits_rd = io_in_bits_rd;	// @[src/main/EXU.scala:6:7]
  assign io_out_bits_wdata =
    (io_in_bits_decode_funct3[1:0] == 2'h0 ? {2{{2{io_in_bits_src3[7:0]}}}} : 32'h0)
    | (io_in_bits_decode_funct3[1:0] == 2'h2 ? io_in_bits_src3 : 32'h0);	// @[src/main/EXU.scala:6:7, :21:41, :26:{26,37}, src/main/scala/chisel3/util/Mux.scala:30:73, src/main/utils/Utils.scala:21:92]
  assign io_out_bits_wmask =
    (io_in_bits_decode_funct3[1:0] == 2'h0 ? casez_tmp[3:0] : 4'h0)
    | {4{io_in_bits_decode_funct3[1:0] == 2'h2}};	// @[src/main/EXU.scala:6:7, :21:41, src/main/scala/chisel3/util/Mux.scala:30:73, src/main/utils/Utils.scala:21:92]
  assign io_out_bits_decode_funct3 = io_in_bits_decode_funct3;	// @[src/main/EXU.scala:6:7]
  assign io_out_bits_decode_isLoad = io_in_bits_decode_isLoad;	// @[src/main/EXU.scala:6:7]
  assign io_out_bits_decode_isStore = io_in_bits_decode_isStore;	// @[src/main/EXU.scala:6:7]
  assign io_redirect_valid = io_in_valid & io_in_bits_decode_isJmp;	// @[src/main/EXU.scala:6:7, :41:36]
  assign io_redirect_dnpc = {_sum_T[31:2], 2'h0};	// @[src/main/EXU.scala:6:7, :17:21, src/main/IFU.scala:7:{25,40}]
endmodule

module LSU(	// @[src/main/LSU.scala:6:7]
  input         clock,	// @[src/main/LSU.scala:6:7]
                reset,	// @[src/main/LSU.scala:6:7]
                io_in_valid,	// @[src/main/LSU.scala:7:14]
  input  [31:0] io_in_bits_res,	// @[src/main/LSU.scala:7:14]
  input         io_in_bits_wen,	// @[src/main/LSU.scala:7:14]
  input  [3:0]  io_in_bits_rd,	// @[src/main/LSU.scala:7:14]
  input  [31:0] io_in_bits_wdata,	// @[src/main/LSU.scala:7:14]
  input  [3:0]  io_in_bits_wmask,	// @[src/main/LSU.scala:7:14]
  input  [2:0]  io_in_bits_decode_funct3,	// @[src/main/LSU.scala:7:14]
  input         io_in_bits_decode_isLoad,	// @[src/main/LSU.scala:7:14]
                io_in_bits_decode_isStore,	// @[src/main/LSU.scala:7:14]
  output        io_out_valid,	// @[src/main/LSU.scala:7:14]
  output [31:0] io_out_bits_res,	// @[src/main/LSU.scala:7:14]
  output        io_out_bits_wen,	// @[src/main/LSU.scala:7:14]
  output [3:0]  io_out_bits_rd,	// @[src/main/LSU.scala:7:14]
  output [31:0] io_mem_addr,	// @[src/main/LSU.scala:7:14]
  output        io_mem_reqValid,	// @[src/main/LSU.scala:7:14]
                io_mem_size,	// @[src/main/LSU.scala:7:14]
                io_mem_wen,	// @[src/main/LSU.scala:7:14]
  output [31:0] io_mem_wdata,	// @[src/main/LSU.scala:7:14]
  output [3:0]  io_mem_wmask,	// @[src/main/LSU.scala:7:14]
  input  [31:0] io_mem_rdata,	// @[src/main/LSU.scala:7:14]
  input         io_mem_respValid	// @[src/main/LSU.scala:7:14]
);

  wire       isValidLoadStore =
    (io_in_bits_decode_isLoad | io_in_bits_decode_isStore) & io_in_valid;	// @[src/main/LSU.scala:17:{45,67}]
  reg        state;	// @[src/main/LSU.scala:25:22]
  wire       loadByte = io_in_bits_decode_funct3[1:0] == 2'h0;	// @[src/main/LSU.scala:33:24, :59:33]
  reg  [7:0] casez_tmp;	// @[src/main/LSU.scala:63:76]
  always_comb begin	// @[src/main/LSU.scala:63:76]
    casez (io_in_bits_res[1:0])	// @[src/main/LSU.scala:54:35, :63:76]
      2'b00:
        casez_tmp = io_mem_rdata[7:0];	// @[src/main/LSU.scala:41:44, :63:76]
      2'b01:
        casez_tmp = io_mem_rdata[15:8];	// @[src/main/LSU.scala:41:44, :63:76]
      2'b10:
        casez_tmp = io_mem_rdata[23:16];	// @[src/main/LSU.scala:41:44, :63:76]
      default:
        casez_tmp = io_mem_rdata[31:24];	// @[src/main/LSU.scala:41:44, :63:76]
    endcase	// @[src/main/LSU.scala:54:35, :63:76]
  end // always_comb
  always @(posedge clock) begin	// @[src/main/LSU.scala:6:7]
    if (reset)	// @[src/main/LSU.scala:6:7]
      state <= 1'h0;	// @[src/main/LSU.scala:6:7, :25:22]
    else	// @[src/main/LSU.scala:6:7]
      state <= ~state & isValidLoadStore | state & ~io_mem_respValid;	// @[src/main/LSU.scala:17:67, :25:22, :28:33, src/main/scala/chisel3/util/Mux.scala:30:73, src/main/utils/Utils.scala:21:92]
  end // always @(posedge)
  `ifdef ENABLE_INITIAL_REG_	// @[src/main/LSU.scala:6:7]
    `ifdef FIRRTL_BEFORE_INITIAL	// @[src/main/LSU.scala:6:7]
      `FIRRTL_BEFORE_INITIAL	// @[src/main/LSU.scala:6:7]
    `endif // FIRRTL_BEFORE_INITIAL
    logic [31:0] _RANDOM[0:0];	// @[src/main/LSU.scala:6:7]
    initial begin	// @[src/main/LSU.scala:6:7]
      `ifdef INIT_RANDOM_PROLOG_	// @[src/main/LSU.scala:6:7]
        `INIT_RANDOM_PROLOG_	// @[src/main/LSU.scala:6:7]
      `endif // INIT_RANDOM_PROLOG_
      `ifdef RANDOMIZE_REG_INIT	// @[src/main/LSU.scala:6:7]
        _RANDOM[/*Zero width*/ 1'b0] = `RANDOM;	// @[src/main/LSU.scala:6:7]
        state = _RANDOM[/*Zero width*/ 1'b0][0];	// @[src/main/LSU.scala:6:7, :25:22]
      `endif // RANDOMIZE_REG_INIT
    end // initial
    `ifdef FIRRTL_AFTER_INITIAL	// @[src/main/LSU.scala:6:7]
      `FIRRTL_AFTER_INITIAL	// @[src/main/LSU.scala:6:7]
    `endif // FIRRTL_AFTER_INITIAL
  `endif // ENABLE_INITIAL_REG_
  assign io_out_valid =
    ~state & io_in_valid & ~io_in_bits_decode_isLoad & ~io_in_bits_decode_isStore | state
    & io_mem_respValid;	// @[src/main/LSU.scala:6:7, :25:22, :86:{47,62,65,83,86,106}, :87:51, src/main/utils/Utils.scala:21:92]
  assign io_out_bits_res =
    io_in_bits_decode_isLoad
      ? {loadByte
           ? (io_in_bits_decode_funct3[2] ? 16'h0 : {16{casez_tmp[7]}})
           : io_mem_rdata[31:16],
         loadByte
           ? (io_in_bits_decode_funct3[2] ? 8'h0 : {8{casez_tmp[7]}})
           : io_in_bits_res[1] ? io_mem_rdata[31:24] : io_mem_rdata[15:8],
         casez_tmp}
      : io_in_bits_res;	// @[src/main/LSU.scala:6:7, :41:44, :43:42, :53:35, :58:26, :59:33, :62:{59,86}, :63:76, :68:23, :69:23, :74:23, :77:17]
  assign io_out_bits_wen = io_in_bits_wen;	// @[src/main/LSU.scala:6:7]
  assign io_out_bits_rd = io_in_bits_rd;	// @[src/main/LSU.scala:6:7]
  assign io_mem_addr = io_in_bits_res;	// @[src/main/LSU.scala:6:7]
  assign io_mem_reqValid = isValidLoadStore & ~state;	// @[src/main/LSU.scala:6:7, :17:67, :25:22, :31:39, src/main/utils/Utils.scala:21:92]
  assign io_mem_size = io_in_bits_decode_funct3[0];	// @[src/main/LSU.scala:6:7, :33:{15,24}]
  assign io_mem_wen = io_in_bits_decode_isStore;	// @[src/main/LSU.scala:6:7]
  assign io_mem_wdata = io_in_bits_wdata;	// @[src/main/LSU.scala:6:7]
  assign io_mem_wmask = io_in_bits_wmask;	// @[src/main/LSU.scala:6:7]
endmodule

module WBU(	// @[src/main/WBU.scala:5:7]
  input         io_in_valid,	// @[src/main/WBU.scala:6:14]
  input  [31:0] io_in_bits_res,	// @[src/main/WBU.scala:6:14]
  input         io_in_bits_wen,	// @[src/main/WBU.scala:6:14]
  input  [3:0]  io_in_bits_rd,	// @[src/main/WBU.scala:6:14]
  output [31:0] io_wb_res,	// @[src/main/WBU.scala:6:14]
  output        io_wb_wen,	// @[src/main/WBU.scala:6:14]
  output [3:0]  io_wb_rd	// @[src/main/WBU.scala:6:14]
);

  assign io_wb_res = io_in_bits_res;	// @[src/main/WBU.scala:5:7]
  assign io_wb_wen = io_in_bits_wen & io_in_valid;	// @[src/main/WBU.scala:5:7, :12:31]
  assign io_wb_rd = io_in_bits_rd;	// @[src/main/WBU.scala:5:7]
endmodule

module IMEMBridge(	// @[src/main/Memory.scala:6:7]
  input         clock,	// @[src/main/Memory.scala:6:7]
                reset,	// @[src/main/Memory.scala:6:7]
  input  [31:0] io_in_pc,	// @[src/main/Memory.scala:7:14]
  input         io_in_pcValid,	// @[src/main/Memory.scala:7:14]
  output [31:0] io_in_inst,	// @[src/main/Memory.scala:7:14]
  output        io_in_instValid,	// @[src/main/Memory.scala:7:14]
  input         io_out_arready,	// @[src/main/Memory.scala:7:14]
  output        io_out_arvalid,	// @[src/main/Memory.scala:7:14]
  output [31:0] io_out_araddr,	// @[src/main/Memory.scala:7:14]
  output        io_out_rready,	// @[src/main/Memory.scala:7:14]
  input         io_out_rvalid,	// @[src/main/Memory.scala:7:14]
  input  [31:0] io_out_rdata	// @[src/main/Memory.scala:7:14]
);

  reg  [1:0]  state;	// @[src/main/Memory.scala:16:22]
  wire        _io_out_arvalid_T = state == 2'h0;	// @[src/main/Memory.scala:6:7, :16:22, src/main/utils/Utils.scala:21:92]
  wire        _io_out_arvalid_T_2 = state == 2'h1;	// @[src/main/Memory.scala:6:7, :16:22, src/main/utils/Utils.scala:21:92]
  wire        io_out_rready_0 = state == 2'h2;	// @[src/main/Memory.scala:6:7, :16:22, src/main/utils/Utils.scala:21:92]
  wire        io_in_instValid_0 = io_out_rready_0 & io_out_rvalid;	// @[src/main/scala/chisel3/util/Decoupled.scala:51:35, src/main/utils/Utils.scala:21:92]
  reg  [31:0] io_in_inst_r;	// @[src/main/Memory.scala:33:65]
  always @(posedge clock) begin	// @[src/main/Memory.scala:6:7]
    if (reset)	// @[src/main/Memory.scala:6:7]
      state <= 2'h0;	// @[src/main/Memory.scala:6:7, :16:22]
    else	// @[src/main/Memory.scala:6:7]
      state <=
        (_io_out_arvalid_T & io_in_pcValid | _io_out_arvalid_T_2
           ? (io_out_arready ? 2'h2 : 2'h1)
           : 2'h0) | (io_out_rready_0 ? {~io_out_rvalid, 1'h0} : 2'h0);	// @[src/main/Memory.scala:6:7, :16:22, :17:28, :19:33, :21:33, src/main/scala/chisel3/util/Mux.scala:30:73, src/main/utils/Utils.scala:21:92]
    if (io_in_instValid_0)	// @[src/main/scala/chisel3/util/Decoupled.scala:51:35]
      io_in_inst_r <= io_out_rdata;	// @[src/main/Memory.scala:33:65]
  end // always @(posedge)
  `ifdef ENABLE_INITIAL_REG_	// @[src/main/Memory.scala:6:7]
    `ifdef FIRRTL_BEFORE_INITIAL	// @[src/main/Memory.scala:6:7]
      `FIRRTL_BEFORE_INITIAL	// @[src/main/Memory.scala:6:7]
    `endif // FIRRTL_BEFORE_INITIAL
    logic [31:0] _RANDOM[0:1];	// @[src/main/Memory.scala:6:7]
    initial begin	// @[src/main/Memory.scala:6:7]
      `ifdef INIT_RANDOM_PROLOG_	// @[src/main/Memory.scala:6:7]
        `INIT_RANDOM_PROLOG_	// @[src/main/Memory.scala:6:7]
      `endif // INIT_RANDOM_PROLOG_
      `ifdef RANDOMIZE_REG_INIT	// @[src/main/Memory.scala:6:7]
        for (logic [1:0] i = 2'h0; i < 2'h2; i += 2'h1) begin
          _RANDOM[i[0]] = `RANDOM;	// @[src/main/Memory.scala:6:7]
        end	// @[src/main/Memory.scala:6:7]
        state = _RANDOM[1'h0][1:0];	// @[src/main/Memory.scala:6:7, :16:22]
        io_in_inst_r = {_RANDOM[1'h0][31:2], _RANDOM[1'h1][1:0]};	// @[src/main/Memory.scala:6:7, :16:22, :33:65]
      `endif // RANDOMIZE_REG_INIT
    end // initial
    `ifdef FIRRTL_AFTER_INITIAL	// @[src/main/Memory.scala:6:7]
      `FIRRTL_AFTER_INITIAL	// @[src/main/Memory.scala:6:7]
    `endif // FIRRTL_AFTER_INITIAL
  `endif // ENABLE_INITIAL_REG_
  assign io_in_inst = io_in_instValid_0 ? io_out_rdata : io_in_inst_r;	// @[src/main/Memory.scala:6:7, :33:{20,65}, src/main/scala/chisel3/util/Decoupled.scala:51:35]
  assign io_in_instValid = io_in_instValid_0;	// @[src/main/Memory.scala:6:7, src/main/scala/chisel3/util/Decoupled.scala:51:35]
  assign io_out_arvalid = io_in_pcValid & _io_out_arvalid_T | _io_out_arvalid_T_2;	// @[src/main/Memory.scala:6:7, :25:{37,68}, src/main/utils/Utils.scala:21:92]
  assign io_out_araddr = io_in_pc;	// @[src/main/Memory.scala:6:7]
  assign io_out_rready = io_out_rready_0;	// @[src/main/Memory.scala:6:7, src/main/utils/Utils.scala:21:92]
endmodule

module DMEMBridge(	// @[src/main/Memory.scala:37:7]
  input         clock,	// @[src/main/Memory.scala:37:7]
                reset,	// @[src/main/Memory.scala:37:7]
  input  [31:0] io_in_addr,	// @[src/main/Memory.scala:38:14]
  input         io_in_reqValid,	// @[src/main/Memory.scala:38:14]
                io_in_size,	// @[src/main/Memory.scala:38:14]
                io_in_wen,	// @[src/main/Memory.scala:38:14]
  input  [31:0] io_in_wdata,	// @[src/main/Memory.scala:38:14]
  input  [3:0]  io_in_wmask,	// @[src/main/Memory.scala:38:14]
  output [31:0] io_in_rdata,	// @[src/main/Memory.scala:38:14]
  output        io_in_respValid,	// @[src/main/Memory.scala:38:14]
  input         io_out_awready,	// @[src/main/Memory.scala:38:14]
  output        io_out_awvalid,	// @[src/main/Memory.scala:38:14]
  output [31:0] io_out_awaddr,	// @[src/main/Memory.scala:38:14]
  output [2:0]  io_out_awsize,	// @[src/main/Memory.scala:38:14]
  input         io_out_wready,	// @[src/main/Memory.scala:38:14]
  output        io_out_wvalid,	// @[src/main/Memory.scala:38:14]
  output [31:0] io_out_wdata,	// @[src/main/Memory.scala:38:14]
  output [3:0]  io_out_wstrb,	// @[src/main/Memory.scala:38:14]
  output        io_out_bready,	// @[src/main/Memory.scala:38:14]
  input         io_out_bvalid,	// @[src/main/Memory.scala:38:14]
                io_out_arready,	// @[src/main/Memory.scala:38:14]
  output        io_out_arvalid,	// @[src/main/Memory.scala:38:14]
  output [31:0] io_out_araddr,	// @[src/main/Memory.scala:38:14]
  output [2:0]  io_out_arsize,	// @[src/main/Memory.scala:38:14]
  output        io_out_rready,	// @[src/main/Memory.scala:38:14]
  input         io_out_rvalid,	// @[src/main/Memory.scala:38:14]
  input  [31:0] io_out_rdata	// @[src/main/Memory.scala:38:14]
);

  wire       io_out_bready_0;	// @[src/main/utils/Utils.scala:21:92]
  wire       io_out_rready_0;	// @[src/main/utils/Utils.scala:21:92]
  wire       isValidLoad = io_in_reqValid & ~io_in_wen;	// @[src/main/Memory.scala:43:{37,40}]
  wire       isValidStore = io_in_reqValid & io_in_wen;	// @[src/main/Memory.scala:44:37]
  reg  [2:0] state;	// @[src/main/Memory.scala:50:22]
  wire       _io_in_respValid_T = io_out_rready_0 & io_out_rvalid;	// @[src/main/scala/chisel3/util/Decoupled.scala:51:35, src/main/utils/Utils.scala:21:92]
  wire       _io_in_respValid_T_1 = io_out_bready_0 & io_out_bvalid;	// @[src/main/scala/chisel3/util/Decoupled.scala:51:35, src/main/utils/Utils.scala:21:92]
  wire       _io_out_wvalid_T = state == 3'h0;	// @[src/main/Memory.scala:50:22, src/main/utils/Utils.scala:21:92]
  wire       _io_out_arvalid_T_2 = state == 3'h1;	// @[src/main/Memory.scala:50:22, src/main/utils/Utils.scala:21:92]
  assign io_out_rready_0 = state == 3'h2;	// @[src/main/Memory.scala:50:22, src/main/utils/Utils.scala:21:92]
  wire       _io_out_awvalid_T_2 = state == 3'h3;	// @[src/main/Memory.scala:50:22, src/main/utils/Utils.scala:21:92]
  wire       _io_out_awvalid_T_3 = state == 3'h5;	// @[src/main/Memory.scala:50:22, src/main/utils/Utils.scala:21:92]
  wire       _io_out_wvalid_T_2 = state == 3'h4;	// @[src/main/Memory.scala:50:22, src/main/utils/Utils.scala:21:92]
  assign io_out_bready_0 = state == 3'h6;	// @[src/main/Memory.scala:50:22, src/main/utils/Utils.scala:21:92]
  wire       io_out_arvalid_0 = _io_out_wvalid_T & isValidLoad | _io_out_arvalid_T_2;	// @[src/main/Memory.scala:43:37, :68:{50,66}, src/main/utils/Utils.scala:21:92]
  wire [2:0] io_out_arsize_0 = {2'h0, io_in_size};	// @[src/main/Memory.scala:70:23]
  wire       io_out_awvalid_0 =
    _io_out_wvalid_T & isValidStore | (|{_io_out_awvalid_T_3, _io_out_awvalid_T_2});	// @[src/main/Memory.scala:44:37, :72:{50,67,83}, src/main/utils/Utils.scala:21:92]
  wire       io_out_wvalid_0 = _io_out_wvalid_T & isValidStore | _io_out_wvalid_T_2;	// @[src/main/Memory.scala:44:37, :74:{50,67}, src/main/utils/Utils.scala:21:92]
  wire       _state_T_10 = io_out_arready & io_out_arvalid_0;	// @[src/main/Memory.scala:68:66, src/main/scala/chisel3/util/Decoupled.scala:51:35]
  wire       _state_T_18 = io_out_awready & io_out_awvalid_0;	// @[src/main/Memory.scala:72:67, src/main/scala/chisel3/util/Decoupled.scala:51:35]
  wire       _state_T_20 = io_out_wready & io_out_wvalid_0;	// @[src/main/Memory.scala:74:67, src/main/scala/chisel3/util/Decoupled.scala:51:35]
  wire [2:0] _state_T_32 =
    _io_out_wvalid_T
      ? (isValidLoad
           ? {1'h0, _state_T_10 ? 2'h2 : 2'h1}
           : isValidStore
               ? (_state_T_18 ? {1'h1, _state_T_20, 1'h0} : _state_T_20 ? 3'h5 : 3'h0)
               : 3'h0)
      : 3'h0;	// @[src/main/Memory.scala:37:7, :43:37, :44:37, :50:22, :52:{33,50}, :53:34, :54:36, :55:38, :56:38, src/main/scala/chisel3/util/Decoupled.scala:51:35, src/main/scala/chisel3/util/Mux.scala:30:73, src/main/utils/Utils.scala:21:92]
  always @(posedge clock) begin	// @[src/main/Memory.scala:37:7]
    if (reset)	// @[src/main/Memory.scala:37:7]
      state <= 3'h0;	// @[src/main/Memory.scala:50:22]
    else	// @[src/main/Memory.scala:37:7]
      state <=
        {_state_T_32[2],
         _state_T_32[1:0] | (_io_out_arvalid_T_2 ? (_state_T_10 ? 2'h2 : 2'h1) : 2'h0)
           | (io_out_rready_0 ? {~_io_in_respValid_T, 1'h0} : 2'h0)}
        | (_io_out_awvalid_T_2 ? (_state_T_18 ? {1'h1, _state_T_20, 1'h0} : 3'h3) : 3'h0)
        | (_io_out_awvalid_T_3 ? (_state_T_18 ? 3'h6 : 3'h5) : 3'h0)
        | (_io_out_wvalid_T_2 ? {1'h1, _state_T_20, 1'h0} : 3'h0)
        | (~io_out_bready_0 | _io_in_respValid_T_1 ? 3'h0 : 3'h6);	// @[src/main/Memory.scala:37:7, :50:22, :58:33, :59:33, :60:33, :61:35, :63:33, :64:33, src/main/scala/chisel3/util/Decoupled.scala:51:35, src/main/scala/chisel3/util/Mux.scala:30:73, src/main/utils/Utils.scala:21:92]
  end // always @(posedge)
  `ifdef ENABLE_INITIAL_REG_	// @[src/main/Memory.scala:37:7]
    `ifdef FIRRTL_BEFORE_INITIAL	// @[src/main/Memory.scala:37:7]
      `FIRRTL_BEFORE_INITIAL	// @[src/main/Memory.scala:37:7]
    `endif // FIRRTL_BEFORE_INITIAL
    logic [31:0] _RANDOM[0:0];	// @[src/main/Memory.scala:37:7]
    initial begin	// @[src/main/Memory.scala:37:7]
      `ifdef INIT_RANDOM_PROLOG_	// @[src/main/Memory.scala:37:7]
        `INIT_RANDOM_PROLOG_	// @[src/main/Memory.scala:37:7]
      `endif // INIT_RANDOM_PROLOG_
      `ifdef RANDOMIZE_REG_INIT	// @[src/main/Memory.scala:37:7]
        _RANDOM[/*Zero width*/ 1'b0] = `RANDOM;	// @[src/main/Memory.scala:37:7]
        state = _RANDOM[/*Zero width*/ 1'b0][2:0];	// @[src/main/Memory.scala:37:7, :50:22]
      `endif // RANDOMIZE_REG_INIT
    end // initial
    `ifdef FIRRTL_AFTER_INITIAL	// @[src/main/Memory.scala:37:7]
      `FIRRTL_AFTER_INITIAL	// @[src/main/Memory.scala:37:7]
    `endif // FIRRTL_AFTER_INITIAL
  `endif // ENABLE_INITIAL_REG_
  assign io_in_rdata = io_out_rdata;	// @[src/main/Memory.scala:37:7]
  assign io_in_respValid = _io_in_respValid_T | _io_in_respValid_T_1;	// @[src/main/Memory.scala:37:7, :80:36, src/main/scala/chisel3/util/Decoupled.scala:51:35]
  assign io_out_awvalid = io_out_awvalid_0;	// @[src/main/Memory.scala:37:7, :72:67]
  assign io_out_awaddr = io_in_addr;	// @[src/main/Memory.scala:37:7]
  assign io_out_awsize = io_out_arsize_0;	// @[src/main/Memory.scala:37:7, :70:23]
  assign io_out_wvalid = io_out_wvalid_0;	// @[src/main/Memory.scala:37:7, :74:67]
  assign io_out_wdata = io_in_wdata;	// @[src/main/Memory.scala:37:7]
  assign io_out_wstrb = io_in_wmask;	// @[src/main/Memory.scala:37:7]
  assign io_out_bready = io_out_bready_0;	// @[src/main/Memory.scala:37:7, src/main/utils/Utils.scala:21:92]
  assign io_out_arvalid = io_out_arvalid_0;	// @[src/main/Memory.scala:37:7, :68:66]
  assign io_out_araddr = io_in_addr;	// @[src/main/Memory.scala:37:7]
  assign io_out_arsize = io_out_arsize_0;	// @[src/main/Memory.scala:37:7, :70:23]
  assign io_out_rready = io_out_rready_0;	// @[src/main/Memory.scala:37:7, src/main/utils/Utils.scala:21:92]
endmodule

module Arbiter2_AXI4LiteBundleA(	// @[src/main/scala/chisel3/util/Arbiter.scala:133:7]
  output        io_in_0_ready,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  input         io_in_0_valid,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  input  [31:0] io_in_0_bits_addr,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  input  [2:0]  io_in_0_bits_size,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  output        io_in_1_ready,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  input         io_in_1_valid,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  input  [31:0] io_in_1_bits_addr,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  input         io_out_ready,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  output        io_out_valid,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  output [31:0] io_out_bits_addr,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  output [2:0]  io_out_bits_size,	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
  output        io_chosen	// @[src/main/scala/chisel3/util/Arbiter.scala:140:14]
);

  assign io_in_0_ready = io_out_ready;	// @[src/main/scala/chisel3/util/Arbiter.scala:133:7]
  assign io_in_1_ready = ~io_in_0_valid & io_out_ready;	// @[src/main/scala/chisel3/util/Arbiter.scala:45:78, :133:7, :153:19]
  assign io_out_valid = io_in_0_valid | io_in_1_valid;	// @[src/main/scala/chisel3/util/Arbiter.scala:133:7, :154:31]
  assign io_out_bits_addr = io_in_0_valid ? io_in_0_bits_addr : io_in_1_bits_addr;	// @[src/main/scala/chisel3/util/Arbiter.scala:133:7, :143:15, :145:26, :147:19]
  assign io_out_bits_size = io_in_0_valid ? io_in_0_bits_size : 3'h2;	// @[src/main/scala/chisel3/util/Arbiter.scala:133:7, :140:14, :143:15, :145:26, :147:19]
  assign io_chosen = ~io_in_0_valid;	// @[src/main/scala/chisel3/util/Arbiter.scala:133:7, :142:13, :145:26, :146:17]
endmodule

module AXI4LiteCrossbarNto1(	// @[src/main/AXI4.scala:117:7]
  input         clock,	// @[src/main/AXI4.scala:117:7]
                reset,	// @[src/main/AXI4.scala:117:7]
  output        io_in_0_arready,	// @[src/main/AXI4.scala:118:14]
  input         io_in_0_arvalid,	// @[src/main/AXI4.scala:118:14]
  input  [31:0] io_in_0_araddr,	// @[src/main/AXI4.scala:118:14]
  input  [2:0]  io_in_0_arsize,	// @[src/main/AXI4.scala:118:14]
  input         io_in_0_rready,	// @[src/main/AXI4.scala:118:14]
  output        io_in_0_rvalid,	// @[src/main/AXI4.scala:118:14]
  output [31:0] io_in_0_rdata,	// @[src/main/AXI4.scala:118:14]
  output        io_in_1_arready,	// @[src/main/AXI4.scala:118:14]
  input         io_in_1_arvalid,	// @[src/main/AXI4.scala:118:14]
  input  [31:0] io_in_1_araddr,	// @[src/main/AXI4.scala:118:14]
  input         io_in_1_rready,	// @[src/main/AXI4.scala:118:14]
  output        io_in_1_rvalid,	// @[src/main/AXI4.scala:118:14]
  output [31:0] io_in_1_rdata,	// @[src/main/AXI4.scala:118:14]
  input         io_out_arready,	// @[src/main/AXI4.scala:118:14]
  output        io_out_arvalid,	// @[src/main/AXI4.scala:118:14]
  output [31:0] io_out_araddr,	// @[src/main/AXI4.scala:118:14]
  output [2:0]  io_out_arsize,	// @[src/main/AXI4.scala:118:14]
  output        io_out_rready,	// @[src/main/AXI4.scala:118:14]
  input         io_out_rvalid,	// @[src/main/AXI4.scala:118:14]
  input  [31:0] io_out_rdata	// @[src/main/AXI4.scala:118:14]
);

  wire _inputArbRead_io_out_valid;	// @[src/main/AXI4.scala:126:28]
  wire _inputArbRead_io_chosen;	// @[src/main/AXI4.scala:126:28]
  reg  stateRead;	// @[src/main/AXI4.scala:130:26]
  reg  chosenRead;	// @[src/main/AXI4.scala:137:29]
  wire inputArbRead_io_out_ready = io_out_arready & ~stateRead;	// @[src/main/AXI4.scala:130:26, :141:37, src/main/utils/Utils.scala:21:92]
  wire io_out_rready_0 = chosenRead ? io_in_1_rready : io_in_0_rready;	// @[src/main/AXI4.scala:137:29, :146:18]
  wire _chosenRead_T = inputArbRead_io_out_ready & _inputArbRead_io_out_valid;	// @[src/main/AXI4.scala:126:28, :141:37, src/main/scala/chisel3/util/Decoupled.scala:51:35]
  always @(posedge clock) begin	// @[src/main/AXI4.scala:117:7]
    if (reset) begin	// @[src/main/AXI4.scala:117:7]
      stateRead <= 1'h0;	// @[src/main/AXI4.scala:117:7, :130:26]
      chosenRead <= 1'h0;	// @[src/main/AXI4.scala:117:7, :137:29]
    end
    else begin	// @[src/main/AXI4.scala:117:7]
      stateRead <=
        ~stateRead & _chosenRead_T | stateRead & ~(io_out_rready_0 & io_out_rvalid);	// @[src/main/AXI4.scala:130:26, :133:26, :146:18, src/main/scala/chisel3/util/Decoupled.scala:51:35, src/main/scala/chisel3/util/Mux.scala:30:73, src/main/utils/Utils.scala:21:92]
      if (~stateRead & _chosenRead_T)	// @[src/main/AXI4.scala:130:26, :137:70, src/main/scala/chisel3/util/Decoupled.scala:51:35, src/main/utils/Utils.scala:21:92]
        chosenRead <= _inputArbRead_io_chosen;	// @[src/main/AXI4.scala:126:28, :137:29]
    end
  end // always @(posedge)
  `ifdef ENABLE_INITIAL_REG_	// @[src/main/AXI4.scala:117:7]
    `ifdef FIRRTL_BEFORE_INITIAL	// @[src/main/AXI4.scala:117:7]
      `FIRRTL_BEFORE_INITIAL	// @[src/main/AXI4.scala:117:7]
    `endif // FIRRTL_BEFORE_INITIAL
    logic [31:0] _RANDOM[0:0];	// @[src/main/AXI4.scala:117:7]
    initial begin	// @[src/main/AXI4.scala:117:7]
      `ifdef INIT_RANDOM_PROLOG_	// @[src/main/AXI4.scala:117:7]
        `INIT_RANDOM_PROLOG_	// @[src/main/AXI4.scala:117:7]
      `endif // INIT_RANDOM_PROLOG_
      `ifdef RANDOMIZE_REG_INIT	// @[src/main/AXI4.scala:117:7]
        _RANDOM[/*Zero width*/ 1'b0] = `RANDOM;	// @[src/main/AXI4.scala:117:7]
        stateRead = _RANDOM[/*Zero width*/ 1'b0][0];	// @[src/main/AXI4.scala:117:7, :130:26]
        chosenRead = _RANDOM[/*Zero width*/ 1'b0][1];	// @[src/main/AXI4.scala:117:7, :130:26, :137:29]
      `endif // RANDOMIZE_REG_INIT
    end // initial
    `ifdef FIRRTL_AFTER_INITIAL	// @[src/main/AXI4.scala:117:7]
      `FIRRTL_AFTER_INITIAL	// @[src/main/AXI4.scala:117:7]
    `endif // FIRRTL_AFTER_INITIAL
  `endif // ENABLE_INITIAL_REG_
  Arbiter2_AXI4LiteBundleA inputArbRead (	// @[src/main/AXI4.scala:126:28]
    .io_in_0_ready     (io_in_0_arready),
    .io_in_0_valid     (io_in_0_arvalid),
    .io_in_0_bits_addr (io_in_0_araddr),
    .io_in_0_bits_size (io_in_0_arsize),
    .io_in_1_ready     (io_in_1_arready),
    .io_in_1_valid     (io_in_1_arvalid),
    .io_in_1_bits_addr (io_in_1_araddr),
    .io_out_ready      (inputArbRead_io_out_ready),	// @[src/main/AXI4.scala:141:37]
    .io_out_valid      (_inputArbRead_io_out_valid),
    .io_out_bits_addr  (io_out_araddr),
    .io_out_bits_size  (io_out_arsize),
    .io_chosen         (_inputArbRead_io_chosen)
  );	// @[src/main/AXI4.scala:126:28]
  assign io_in_0_rvalid = ~chosenRead & io_out_rvalid;	// @[src/main/AXI4.scala:117:7, :137:29, :144:23, :145:29]
  assign io_in_0_rdata = io_out_rdata;	// @[src/main/AXI4.scala:117:7]
  assign io_in_1_rvalid = chosenRead & io_out_rvalid;	// @[src/main/AXI4.scala:117:7, :137:29, :144:23, :145:29]
  assign io_in_1_rdata = io_out_rdata;	// @[src/main/AXI4.scala:117:7]
  assign io_out_arvalid = _inputArbRead_io_out_valid & ~stateRead;	// @[src/main/AXI4.scala:117:7, :126:28, :130:26, :140:37, src/main/utils/Utils.scala:21:92]
  assign io_out_rready = io_out_rready_0;	// @[src/main/AXI4.scala:117:7, :146:18]
endmodule

module NPC(	// @[src/main/NPC.scala:26:7]
  input         clock,	// @[src/main/NPC.scala:26:7]
                reset,	// @[src/main/NPC.scala:26:7]
                io_master_awready,	// @[src/main/NPC.scala:27:14]
  output        io_master_awvalid,	// @[src/main/NPC.scala:27:14]
  output [31:0] io_master_awaddr,	// @[src/main/NPC.scala:27:14]
  output [2:0]  io_master_awsize,	// @[src/main/NPC.scala:27:14]
  input         io_master_wready,	// @[src/main/NPC.scala:27:14]
  output        io_master_wvalid,	// @[src/main/NPC.scala:27:14]
  output [31:0] io_master_wdata,	// @[src/main/NPC.scala:27:14]
  output [3:0]  io_master_wstrb,	// @[src/main/NPC.scala:27:14]
  output        io_master_bready,	// @[src/main/NPC.scala:27:14]
  input         io_master_bvalid,	// @[src/main/NPC.scala:27:14]
                io_master_arready,	// @[src/main/NPC.scala:27:14]
  output        io_master_arvalid,	// @[src/main/NPC.scala:27:14]
  output [31:0] io_master_araddr,	// @[src/main/NPC.scala:27:14]
  output [2:0]  io_master_arsize,	// @[src/main/NPC.scala:27:14]
  output        io_master_rready,	// @[src/main/NPC.scala:27:14]
  input         io_master_rvalid,	// @[src/main/NPC.scala:27:14]
  input  [31:0] io_master_rdata	// @[src/main/NPC.scala:27:14]
);

  wire        _xbar_io_in_0_arready;	// @[src/main/NPC.scala:57:20]
  wire        _xbar_io_in_0_rvalid;	// @[src/main/NPC.scala:57:20]
  wire [31:0] _xbar_io_in_0_rdata;	// @[src/main/NPC.scala:57:20]
  wire        _xbar_io_in_1_arready;	// @[src/main/NPC.scala:57:20]
  wire        _xbar_io_in_1_rvalid;	// @[src/main/NPC.scala:57:20]
  wire [31:0] _xbar_io_in_1_rdata;	// @[src/main/NPC.scala:57:20]
  wire [31:0] _dmemBridge_io_in_rdata;	// @[src/main/NPC.scala:56:26]
  wire        _dmemBridge_io_in_respValid;	// @[src/main/NPC.scala:56:26]
  wire        _dmemBridge_io_out_arvalid;	// @[src/main/NPC.scala:56:26]
  wire [31:0] _dmemBridge_io_out_araddr;	// @[src/main/NPC.scala:56:26]
  wire [2:0]  _dmemBridge_io_out_arsize;	// @[src/main/NPC.scala:56:26]
  wire        _dmemBridge_io_out_rready;	// @[src/main/NPC.scala:56:26]
  wire [31:0] _imemBridge_io_in_inst;	// @[src/main/NPC.scala:55:26]
  wire        _imemBridge_io_in_instValid;	// @[src/main/NPC.scala:55:26]
  wire        _imemBridge_io_out_arvalid;	// @[src/main/NPC.scala:55:26]
  wire [31:0] _imemBridge_io_out_araddr;	// @[src/main/NPC.scala:55:26]
  wire        _imemBridge_io_out_rready;	// @[src/main/NPC.scala:55:26]
  wire [31:0] _wbu_io_wb_res;	// @[src/main/NPC.scala:44:19]
  wire        _wbu_io_wb_wen;	// @[src/main/NPC.scala:44:19]
  wire [3:0]  _wbu_io_wb_rd;	// @[src/main/NPC.scala:44:19]
  wire        _lsu_io_out_valid;	// @[src/main/NPC.scala:43:19]
  wire [31:0] _lsu_io_out_bits_res;	// @[src/main/NPC.scala:43:19]
  wire        _lsu_io_out_bits_wen;	// @[src/main/NPC.scala:43:19]
  wire [3:0]  _lsu_io_out_bits_rd;	// @[src/main/NPC.scala:43:19]
  wire [31:0] _lsu_io_mem_addr;	// @[src/main/NPC.scala:43:19]
  wire        _lsu_io_mem_reqValid;	// @[src/main/NPC.scala:43:19]
  wire        _lsu_io_mem_size;	// @[src/main/NPC.scala:43:19]
  wire        _lsu_io_mem_wen;	// @[src/main/NPC.scala:43:19]
  wire [31:0] _lsu_io_mem_wdata;	// @[src/main/NPC.scala:43:19]
  wire [3:0]  _lsu_io_mem_wmask;	// @[src/main/NPC.scala:43:19]
  wire        _exu_io_out_valid;	// @[src/main/NPC.scala:42:19]
  wire [31:0] _exu_io_out_bits_res;	// @[src/main/NPC.scala:42:19]
  wire        _exu_io_out_bits_wen;	// @[src/main/NPC.scala:42:19]
  wire [3:0]  _exu_io_out_bits_rd;	// @[src/main/NPC.scala:42:19]
  wire [31:0] _exu_io_out_bits_wdata;	// @[src/main/NPC.scala:42:19]
  wire [3:0]  _exu_io_out_bits_wmask;	// @[src/main/NPC.scala:42:19]
  wire [2:0]  _exu_io_out_bits_decode_funct3;	// @[src/main/NPC.scala:42:19]
  wire        _exu_io_out_bits_decode_isLoad;	// @[src/main/NPC.scala:42:19]
  wire        _exu_io_out_bits_decode_isStore;	// @[src/main/NPC.scala:42:19]
  wire        _exu_io_redirect_valid;	// @[src/main/NPC.scala:42:19]
  wire [31:0] _exu_io_redirect_dnpc;	// @[src/main/NPC.scala:42:19]
  wire        _idu_io_out_valid;	// @[src/main/NPC.scala:41:19]
  wire [31:0] _idu_io_out_bits_src1;	// @[src/main/NPC.scala:41:19]
  wire [31:0] _idu_io_out_bits_src2;	// @[src/main/NPC.scala:41:19]
  wire [31:0] _idu_io_out_bits_src3;	// @[src/main/NPC.scala:41:19]
  wire        _idu_io_out_bits_wen;	// @[src/main/NPC.scala:41:19]
  wire [3:0]  _idu_io_out_bits_rd;	// @[src/main/NPC.scala:41:19]
  wire [2:0]  _idu_io_out_bits_decode_funct3;	// @[src/main/NPC.scala:41:19]
  wire        _idu_io_out_bits_decode_isLoad;	// @[src/main/NPC.scala:41:19]
  wire        _idu_io_out_bits_decode_isStore;	// @[src/main/NPC.scala:41:19]
  wire        _idu_io_out_bits_decode_isJmp;	// @[src/main/NPC.scala:41:19]
  wire        _idu_io_out_bits_decode_isCSR;	// @[src/main/NPC.scala:41:19]
  wire [31:0] _ifu_io_mem_pc;	// @[src/main/NPC.scala:40:19]
  wire        _ifu_io_mem_pcValid;	// @[src/main/NPC.scala:40:19]
  wire        _ifu_io_out_valid;	// @[src/main/NPC.scala:40:19]
  wire [31:0] _ifu_io_out_bits_pc;	// @[src/main/NPC.scala:40:19]
  wire [31:0] _ifu_io_out_bits_inst;	// @[src/main/NPC.scala:40:19]
  IFU ifu (	// @[src/main/NPC.scala:40:19]
    .clock             (clock),
    .reset             (reset),
    .io_mem_pc         (_ifu_io_mem_pc),
    .io_mem_pcValid    (_ifu_io_mem_pcValid),
    .io_mem_inst       (_imemBridge_io_in_inst),	// @[src/main/NPC.scala:55:26]
    .io_mem_instValid  (_imemBridge_io_in_instValid),	// @[src/main/NPC.scala:55:26]
    .io_out_valid      (_ifu_io_out_valid),
    .io_out_bits_pc    (_ifu_io_out_bits_pc),
    .io_out_bits_inst  (_ifu_io_out_bits_inst),
    .io_redirect_valid (_exu_io_redirect_valid),	// @[src/main/NPC.scala:42:19]
    .io_redirect_dnpc  (_exu_io_redirect_dnpc),	// @[src/main/NPC.scala:42:19]
    .io_nextInst       (_lsu_io_out_valid)	// @[src/main/NPC.scala:43:19]
  );	// @[src/main/NPC.scala:40:19]
  IDU idu (	// @[src/main/NPC.scala:41:19]
    .clock                      (clock),
    .io_in_valid                (_ifu_io_out_valid),	// @[src/main/NPC.scala:40:19]
    .io_in_bits_pc              (_ifu_io_out_bits_pc),	// @[src/main/NPC.scala:40:19]
    .io_in_bits_inst            (_ifu_io_out_bits_inst),	// @[src/main/NPC.scala:40:19]
    .io_out_valid               (_idu_io_out_valid),
    .io_out_bits_src1           (_idu_io_out_bits_src1),
    .io_out_bits_src2           (_idu_io_out_bits_src2),
    .io_out_bits_src3           (_idu_io_out_bits_src3),
    .io_out_bits_wen            (_idu_io_out_bits_wen),
    .io_out_bits_rd             (_idu_io_out_bits_rd),
    .io_out_bits_decode_funct3  (_idu_io_out_bits_decode_funct3),
    .io_out_bits_decode_isLoad  (_idu_io_out_bits_decode_isLoad),
    .io_out_bits_decode_isStore (_idu_io_out_bits_decode_isStore),
    .io_out_bits_decode_isJmp   (_idu_io_out_bits_decode_isJmp),
    .io_out_bits_decode_isCSR   (_idu_io_out_bits_decode_isCSR),
    .io_wb_res                  (_wbu_io_wb_res),	// @[src/main/NPC.scala:44:19]
    .io_wb_wen                  (_wbu_io_wb_wen),	// @[src/main/NPC.scala:44:19]
    .io_wb_rd                   (_wbu_io_wb_rd)	// @[src/main/NPC.scala:44:19]
  );	// @[src/main/NPC.scala:41:19]
  EXU exu (	// @[src/main/NPC.scala:42:19]
    .io_in_valid                (_idu_io_out_valid),	// @[src/main/NPC.scala:41:19]
    .io_in_bits_src1            (_idu_io_out_bits_src1),	// @[src/main/NPC.scala:41:19]
    .io_in_bits_src2            (_idu_io_out_bits_src2),	// @[src/main/NPC.scala:41:19]
    .io_in_bits_src3            (_idu_io_out_bits_src3),	// @[src/main/NPC.scala:41:19]
    .io_in_bits_wen             (_idu_io_out_bits_wen),	// @[src/main/NPC.scala:41:19]
    .io_in_bits_rd              (_idu_io_out_bits_rd),	// @[src/main/NPC.scala:41:19]
    .io_in_bits_decode_funct3   (_idu_io_out_bits_decode_funct3),	// @[src/main/NPC.scala:41:19]
    .io_in_bits_decode_isLoad   (_idu_io_out_bits_decode_isLoad),	// @[src/main/NPC.scala:41:19]
    .io_in_bits_decode_isStore  (_idu_io_out_bits_decode_isStore),	// @[src/main/NPC.scala:41:19]
    .io_in_bits_decode_isJmp    (_idu_io_out_bits_decode_isJmp),	// @[src/main/NPC.scala:41:19]
    .io_in_bits_decode_isCSR    (_idu_io_out_bits_decode_isCSR),	// @[src/main/NPC.scala:41:19]
    .io_out_valid               (_exu_io_out_valid),
    .io_out_bits_res            (_exu_io_out_bits_res),
    .io_out_bits_wen            (_exu_io_out_bits_wen),
    .io_out_bits_rd             (_exu_io_out_bits_rd),
    .io_out_bits_wdata          (_exu_io_out_bits_wdata),
    .io_out_bits_wmask          (_exu_io_out_bits_wmask),
    .io_out_bits_decode_funct3  (_exu_io_out_bits_decode_funct3),
    .io_out_bits_decode_isLoad  (_exu_io_out_bits_decode_isLoad),
    .io_out_bits_decode_isStore (_exu_io_out_bits_decode_isStore),
    .io_redirect_valid          (_exu_io_redirect_valid),
    .io_redirect_dnpc           (_exu_io_redirect_dnpc)
  );	// @[src/main/NPC.scala:42:19]
  LSU lsu (	// @[src/main/NPC.scala:43:19]
    .clock                     (clock),
    .reset                     (reset),
    .io_in_valid               (_exu_io_out_valid),	// @[src/main/NPC.scala:42:19]
    .io_in_bits_res            (_exu_io_out_bits_res),	// @[src/main/NPC.scala:42:19]
    .io_in_bits_wen            (_exu_io_out_bits_wen),	// @[src/main/NPC.scala:42:19]
    .io_in_bits_rd             (_exu_io_out_bits_rd),	// @[src/main/NPC.scala:42:19]
    .io_in_bits_wdata          (_exu_io_out_bits_wdata),	// @[src/main/NPC.scala:42:19]
    .io_in_bits_wmask          (_exu_io_out_bits_wmask),	// @[src/main/NPC.scala:42:19]
    .io_in_bits_decode_funct3  (_exu_io_out_bits_decode_funct3),	// @[src/main/NPC.scala:42:19]
    .io_in_bits_decode_isLoad  (_exu_io_out_bits_decode_isLoad),	// @[src/main/NPC.scala:42:19]
    .io_in_bits_decode_isStore (_exu_io_out_bits_decode_isStore),	// @[src/main/NPC.scala:42:19]
    .io_out_valid              (_lsu_io_out_valid),
    .io_out_bits_res           (_lsu_io_out_bits_res),
    .io_out_bits_wen           (_lsu_io_out_bits_wen),
    .io_out_bits_rd            (_lsu_io_out_bits_rd),
    .io_mem_addr               (_lsu_io_mem_addr),
    .io_mem_reqValid           (_lsu_io_mem_reqValid),
    .io_mem_size               (_lsu_io_mem_size),
    .io_mem_wen                (_lsu_io_mem_wen),
    .io_mem_wdata              (_lsu_io_mem_wdata),
    .io_mem_wmask              (_lsu_io_mem_wmask),
    .io_mem_rdata              (_dmemBridge_io_in_rdata),	// @[src/main/NPC.scala:56:26]
    .io_mem_respValid          (_dmemBridge_io_in_respValid)	// @[src/main/NPC.scala:56:26]
  );	// @[src/main/NPC.scala:43:19]
  WBU wbu (	// @[src/main/NPC.scala:44:19]
    .io_in_valid    (_lsu_io_out_valid),	// @[src/main/NPC.scala:43:19]
    .io_in_bits_res (_lsu_io_out_bits_res),	// @[src/main/NPC.scala:43:19]
    .io_in_bits_wen (_lsu_io_out_bits_wen),	// @[src/main/NPC.scala:43:19]
    .io_in_bits_rd  (_lsu_io_out_bits_rd),	// @[src/main/NPC.scala:43:19]
    .io_wb_res      (_wbu_io_wb_res),
    .io_wb_wen      (_wbu_io_wb_wen),
    .io_wb_rd       (_wbu_io_wb_rd)
  );	// @[src/main/NPC.scala:44:19]
  IMEMBridge imemBridge (	// @[src/main/NPC.scala:55:26]
    .clock               (clock),
    .reset               (reset),
    .io_in_pc            (_ifu_io_mem_pc),	// @[src/main/NPC.scala:40:19]
    .io_in_pcValid       (_ifu_io_mem_pcValid),	// @[src/main/NPC.scala:40:19]
    .io_in_inst          (_imemBridge_io_in_inst),
    .io_in_instValid     (_imemBridge_io_in_instValid),
    .io_out_arready     (_xbar_io_in_1_arready),	// @[src/main/NPC.scala:57:20]
    .io_out_arvalid     (_imemBridge_io_out_arvalid),
    .io_out_araddr (_imemBridge_io_out_araddr),
    .io_out_rready      (_imemBridge_io_out_rready),
    .io_out_rvalid      (_xbar_io_in_1_rvalid),	// @[src/main/NPC.scala:57:20]
    .io_out_rdata  (_xbar_io_in_1_rdata)	// @[src/main/NPC.scala:57:20]
  );	// @[src/main/NPC.scala:55:26]
  DMEMBridge dmemBridge (	// @[src/main/NPC.scala:56:26]
    .clock               (clock),
    .reset               (reset),
    .io_in_addr          (_lsu_io_mem_addr),	// @[src/main/NPC.scala:43:19]
    .io_in_reqValid      (_lsu_io_mem_reqValid),	// @[src/main/NPC.scala:43:19]
    .io_in_size          (_lsu_io_mem_size),	// @[src/main/NPC.scala:43:19]
    .io_in_wen           (_lsu_io_mem_wen),	// @[src/main/NPC.scala:43:19]
    .io_in_wdata         (_lsu_io_mem_wdata),	// @[src/main/NPC.scala:43:19]
    .io_in_wmask         (_lsu_io_mem_wmask),	// @[src/main/NPC.scala:43:19]
    .io_in_rdata         (_dmemBridge_io_in_rdata),
    .io_in_respValid     (_dmemBridge_io_in_respValid),
    .io_out_awready     (io_master_awready),
    .io_out_awvalid     (io_master_awvalid),
    .io_out_awaddr (io_master_awaddr),
    .io_out_awsize (io_master_awsize),
    .io_out_wready      (io_master_wready),
    .io_out_wvalid      (io_master_wvalid),
    .io_out_wdata  (io_master_wdata),
    .io_out_wstrb  (io_master_wstrb),
    .io_out_bready      (io_master_bready),
    .io_out_bvalid      (io_master_bvalid),
    .io_out_arready     (_xbar_io_in_0_arready),	// @[src/main/NPC.scala:57:20]
    .io_out_arvalid     (_dmemBridge_io_out_arvalid),
    .io_out_araddr (_dmemBridge_io_out_araddr),
    .io_out_arsize (_dmemBridge_io_out_arsize),
    .io_out_rready      (_dmemBridge_io_out_rready),
    .io_out_rvalid      (_xbar_io_in_0_rvalid),	// @[src/main/NPC.scala:57:20]
    .io_out_rdata  (_xbar_io_in_0_rdata)	// @[src/main/NPC.scala:57:20]
  );	// @[src/main/NPC.scala:56:26]
  AXI4LiteCrossbarNto1 xbar (	// @[src/main/NPC.scala:57:20]
    .clock                (clock),
    .reset                (reset),
    .io_in_0_arready     (_xbar_io_in_0_arready),
    .io_in_0_arvalid     (_dmemBridge_io_out_arvalid),	// @[src/main/NPC.scala:56:26]
    .io_in_0_araddr (_dmemBridge_io_out_araddr),	// @[src/main/NPC.scala:56:26]
    .io_in_0_arsize (_dmemBridge_io_out_arsize),	// @[src/main/NPC.scala:56:26]
    .io_in_0_rready      (_dmemBridge_io_out_rready),	// @[src/main/NPC.scala:56:26]
    .io_in_0_rvalid      (_xbar_io_in_0_rvalid),
    .io_in_0_rdata  (_xbar_io_in_0_rdata),
    .io_in_1_arready     (_xbar_io_in_1_arready),
    .io_in_1_arvalid     (_imemBridge_io_out_arvalid),	// @[src/main/NPC.scala:55:26]
    .io_in_1_araddr (_imemBridge_io_out_araddr),	// @[src/main/NPC.scala:55:26]
    .io_in_1_rready      (_imemBridge_io_out_rready),	// @[src/main/NPC.scala:55:26]
    .io_in_1_rvalid      (_xbar_io_in_1_rvalid),
    .io_in_1_rdata  (_xbar_io_in_1_rdata),
    .io_out_arready      (io_master_arready),
    .io_out_arvalid      (io_master_arvalid),
    .io_out_araddr  (io_master_araddr),
    .io_out_arsize  (io_master_arsize),
    .io_out_rready       (io_master_rready),
    .io_out_rvalid       (io_master_rvalid),
    .io_out_rdata   (io_master_rdata)
  );	// @[src/main/NPC.scala:57:20]
endmodule

module rv_top(	// @[src/main/NPC.scala:75:7]
  input         clock,	// @[src/main/NPC.scala:75:7]
                reset,	// @[src/main/NPC.scala:75:7]
                io_master_awready,	// @[src/main/NPC.scala:76:14]
  output        io_master_awvalid,	// @[src/main/NPC.scala:76:14]
  output [31:0] io_master_awaddr,	// @[src/main/NPC.scala:76:14]
  output [2:0]  io_master_awsize,	// @[src/main/NPC.scala:76:14]
  output [3:0]  io_master_awid,	// @[src/main/NPC.scala:76:14]
  output [7:0]  io_master_awlen,	// @[src/main/NPC.scala:76:14]
  output [1:0]  io_master_awburst,	// @[src/main/NPC.scala:76:14]
  input         io_master_wready,	// @[src/main/NPC.scala:76:14]
  output        io_master_wvalid,	// @[src/main/NPC.scala:76:14]
  output [31:0] io_master_wdata,	// @[src/main/NPC.scala:76:14]
  output [3:0]  io_master_wstrb,	// @[src/main/NPC.scala:76:14]
  output        io_master_wlast,	// @[src/main/NPC.scala:76:14]
                io_master_bready,	// @[src/main/NPC.scala:76:14]
  input         io_master_bvalid,	// @[src/main/NPC.scala:76:14]
  input  [1:0]  io_master_bresp,	// @[src/main/NPC.scala:76:14]
  input  [3:0]  io_master_bid,	// @[src/main/NPC.scala:76:14]
  input         io_master_arready,	// @[src/main/NPC.scala:76:14]
  output        io_master_arvalid,	// @[src/main/NPC.scala:76:14]
  output [31:0] io_master_araddr,	// @[src/main/NPC.scala:76:14]
  output [2:0]  io_master_arsize,	// @[src/main/NPC.scala:76:14]
  output [3:0]  io_master_arid,	// @[src/main/NPC.scala:76:14]
  output [7:0]  io_master_arlen,	// @[src/main/NPC.scala:76:14]
  output [1:0]  io_master_arburst,	// @[src/main/NPC.scala:76:14]
  output        io_master_rready,	// @[src/main/NPC.scala:76:14]
  input         io_master_rvalid,	// @[src/main/NPC.scala:76:14]
  input  [1:0]  io_master_rresp,	// @[src/main/NPC.scala:76:14]
  input  [31:0] io_master_rdata,	// @[src/main/NPC.scala:76:14]
  input         io_master_rlast,	// @[src/main/NPC.scala:76:14]
  input  [3:0]  io_master_rid,	// @[src/main/NPC.scala:76:14]
  input         io_interrupt,	// @[src/main/NPC.scala:76:14]
  output        io_slave_awready,	// @[src/main/NPC.scala:76:14]
  input         io_slave_awvalid,	// @[src/main/NPC.scala:76:14]
  input  [31:0] io_slave_awaddr,	// @[src/main/NPC.scala:76:14]
  input  [2:0]  io_slave_awsize,	// @[src/main/NPC.scala:76:14]
  input  [3:0]  io_slave_awid,	// @[src/main/NPC.scala:76:14]
  input  [7:0]  io_slave_awlen,	// @[src/main/NPC.scala:76:14]
  input  [1:0]  io_slave_awburst,	// @[src/main/NPC.scala:76:14]
  output        io_slave_wready,	// @[src/main/NPC.scala:76:14]
  input         io_slave_wvalid,	// @[src/main/NPC.scala:76:14]
  input  [31:0] io_slave_wdata,	// @[src/main/NPC.scala:76:14]
  input  [3:0]  io_slave_wstrb,	// @[src/main/NPC.scala:76:14]
  input         io_slave_wlast,	// @[src/main/NPC.scala:76:14]
                io_slave_bready,	// @[src/main/NPC.scala:76:14]
  output        io_slave_bvalid,	// @[src/main/NPC.scala:76:14]
  output [1:0]  io_slave_bresp,	// @[src/main/NPC.scala:76:14]
  output [3:0]  io_slave_bid,	// @[src/main/NPC.scala:76:14]
  output        io_slave_arready,	// @[src/main/NPC.scala:76:14]
  input         io_slave_arvalid,	// @[src/main/NPC.scala:76:14]
  input  [31:0] io_slave_araddr,	// @[src/main/NPC.scala:76:14]
  input  [2:0]  io_slave_arsize,	// @[src/main/NPC.scala:76:14]
  input  [3:0]  io_slave_arid,	// @[src/main/NPC.scala:76:14]
  input  [7:0]  io_slave_arlen,	// @[src/main/NPC.scala:76:14]
  input  [1:0]  io_slave_arburst,	// @[src/main/NPC.scala:76:14]
  input         io_slave_rready,	// @[src/main/NPC.scala:76:14]
  output        io_slave_rvalid,	// @[src/main/NPC.scala:76:14]
  output [1:0]  io_slave_rresp,	// @[src/main/NPC.scala:76:14]
  output [31:0] io_slave_rdata,	// @[src/main/NPC.scala:76:14]
  output        io_slave_rlast,	// @[src/main/NPC.scala:76:14]
  output [3:0]  io_slave_rid	// @[src/main/NPC.scala:76:14]
);

  wire _npc_io_master_bready;	// @[src/main/NPC.scala:82:19]
  wire _npc_io_master_rready;	// @[src/main/NPC.scala:82:19]
  `ifndef SYNTHESIS	// @[src/main/NPC.scala:91:11]
    always @(posedge clock) begin	// @[src/main/NPC.scala:91:11]
      if (~reset & _npc_io_master_rready & io_master_rvalid
          & (|io_master_rresp)) begin	// @[src/main/NPC.scala:82:19, :91:{11,35,64}, src/main/scala/chisel3/util/Decoupled.scala:51:35]
        if (`ASSERT_VERBOSE_COND_)	// @[src/main/NPC.scala:91:11]
          $error("Assertion failed\n    at NPC.scala:91 assert(!(npc.io.master.r.fire && npc.io.master.r.bits.resp =/= AXI4Parameters.RESP_OKAY))\n");	// @[src/main/NPC.scala:91:11]
        if (`STOP_COND_)	// @[src/main/NPC.scala:91:11]
          $fatal;	// @[src/main/NPC.scala:91:11]
      end
      if (~reset & _npc_io_master_bready & io_master_bvalid
          & (|io_master_bresp)) begin	// @[src/main/NPC.scala:82:19, :91:11, :92:{11,35,64}, src/main/scala/chisel3/util/Decoupled.scala:51:35]
        if (`ASSERT_VERBOSE_COND_)	// @[src/main/NPC.scala:92:11]
          $error("Assertion failed\n    at NPC.scala:92 assert(!(npc.io.master.b.fire && npc.io.master.b.bits.resp =/= AXI4Parameters.RESP_OKAY))\n");	// @[src/main/NPC.scala:92:11]
        if (`STOP_COND_)	// @[src/main/NPC.scala:92:11]
          $fatal;	// @[src/main/NPC.scala:92:11]
      end
    end // always @(posedge)
  `endif // not def SYNTHESIS
  NPC npc (	// @[src/main/NPC.scala:82:19]
    .clock                  (clock),
    .reset                  (reset),
    .io_master_awready     (io_master_awready),
    .io_master_awvalid     (io_master_awvalid),
    .io_master_awaddr (io_master_awaddr),
    .io_master_awsize (io_master_awsize),
    .io_master_wready      (io_master_wready),
    .io_master_wvalid      (io_master_wvalid),
    .io_master_wdata  (io_master_wdata),
    .io_master_wstrb  (io_master_wstrb),
    .io_master_bready      (_npc_io_master_bready),
    .io_master_bvalid      (io_master_bvalid),
    .io_master_arready     (io_master_arready),
    .io_master_arvalid     (io_master_arvalid),
    .io_master_araddr (io_master_araddr),
    .io_master_arsize (io_master_arsize),
    .io_master_rready      (_npc_io_master_rready),
    .io_master_rvalid      (io_master_rvalid),
    .io_master_rdata  (io_master_rdata)
  );	// @[src/main/NPC.scala:82:19]
  assign io_master_awid = 4'h0;	// @[src/main/NPC.scala:75:7]
  assign io_master_awlen = 8'h0;	// @[src/main/NPC.scala:75:7]
  assign io_master_awburst = 2'h0;	// @[src/main/NPC.scala:75:7]
  assign io_master_wlast = 1'h1;	// @[src/main/NPC.scala:75:7]
  assign io_master_bready = _npc_io_master_bready;	// @[src/main/NPC.scala:75:7, :82:19]
  assign io_master_arid = 4'h0;	// @[src/main/NPC.scala:75:7]
  assign io_master_arlen = 8'h0;	// @[src/main/NPC.scala:75:7]
  assign io_master_arburst = 2'h0;	// @[src/main/NPC.scala:75:7]
  assign io_master_rready = _npc_io_master_rready;	// @[src/main/NPC.scala:75:7, :82:19]
  assign io_slave_awready = 1'h0;	// @[src/main/NPC.scala:75:7]
  assign io_slave_wready = 1'h0;	// @[src/main/NPC.scala:75:7]
  assign io_slave_bvalid = 1'h0;	// @[src/main/NPC.scala:75:7]
  assign io_slave_bresp = 2'h0;	// @[src/main/NPC.scala:75:7]
  assign io_slave_bid = 4'h0;	// @[src/main/NPC.scala:75:7]
  assign io_slave_arready = 1'h0;	// @[src/main/NPC.scala:75:7]
  assign io_slave_rvalid = 1'h0;	// @[src/main/NPC.scala:75:7]
  assign io_slave_rresp = 2'h0;	// @[src/main/NPC.scala:75:7]
  assign io_slave_rdata = 32'h0;	// @[src/main/NPC.scala:75:7]
  assign io_slave_rlast = 1'h0;	// @[src/main/NPC.scala:75:7]
  assign io_slave_rid = 4'h0;	// @[src/main/NPC.scala:75:7]
endmodule

