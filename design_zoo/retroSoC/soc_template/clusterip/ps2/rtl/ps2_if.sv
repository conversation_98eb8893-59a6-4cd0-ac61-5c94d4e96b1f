// Copyright (c) 2023-2024 <PERSON><PERSON> <mi<PERSON><PERSON><PERSON>@ict.ac.cn>
// ps2 is licensed under Mulan PSL v2.
// You can use this software according to the terms and conditions of the Mulan PSL v2.
// You may obtain a copy of Mulan PSL v2 at:
//             http://license.coscl.org.cn/MulanPSL2
// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
// EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
// MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
// See the Mulan PSL v2 for more details.

interface ps2_if ();
  logic ps2_clk_i;
  logic ps2_dat_i;
  logic irq_o;

  modport dut(input ps2_clk_i, input ps2_dat_i, output irq_o);
  modport tb(output ps2_clk_i, output ps2_dat_i, input irq_o);
endinterface