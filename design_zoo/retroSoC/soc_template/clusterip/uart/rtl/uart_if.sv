// Copyright (c) 2023-2024 <PERSON><PERSON> <miao<PERSON><PERSON>@ict.ac.cn>
// uart is licensed under Mulan PSL v2.
// You can use this software according to the terms and conditions of the Mulan PSL v2.
// You may obtain a copy of Mulan PSL v2 at:
//             http://license.coscl.org.cn/MulanPSL2
// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
// EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
// MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
// See the Mulan PSL v2 for more details.

interface uart_if ();
  logic uart_rx_i;
  logic uart_tx_o;
  logic irq_o;

  modport dut(input uart_rx_i, output uart_tx_o, output irq_o);
  modport tb(output uart_rx_i, input uart_tx_o, input irq_o);
endinterface