module _wrapper(
  (* keep *) input         clock,
  (* keep *) input         reset,
  (* keep *) input         io_master_awready,
  (* keep *) output        io_master_awvalid,
  (* keep *) output [31:0] io_master_awaddr,
  (* keep *) output [2:0]  io_master_awsize,
  (* keep *) output [3:0]  io_master_awid,
  (* keep *) output [7:0]  io_master_awlen,
  (* keep *) output [1:0]  io_master_awburst,
  (* keep *) input         io_master_wready,
  (* keep *) output        io_master_wvalid,
  (* keep *) output [31:0] io_master_wdata,
  (* keep *) output [3:0]  io_master_wstrb,
  (* keep *) output        io_master_wlast,
  (* keep *)               io_master_bready,
  (* keep *) input         io_master_bvalid,
  (* keep *) input  [1:0]  io_master_bresp,
  (* keep *) input  [3:0]  io_master_bid,
  (* keep *) input         io_master_arready,
  (* keep *) output        io_master_arvalid,
  (* keep *) output [31:0] io_master_araddr,
  (* keep *) output [2:0]  io_master_arsize,
  (* keep *) output [3:0]  io_master_arid,
  (* keep *) output [7:0]  io_master_arlen,
  (* keep *) output [1:0]  io_master_arburst,
  (* keep *) output        io_master_rready,
  (* keep *) input         io_master_rvalid,
  (* keep *) input  [1:0]  io_master_rresp,
  (* keep *) input  [31:0] io_master_rdata,
  (* keep *) input         io_master_rlast,
  (* keep *) input  [3:0]  io_master_rid,
  (* keep *) input         io_interrupt,
  (* keep *) output        io_slave_awready,
  (* keep *) input         io_slave_awvalid,
  (* keep *) input  [31:0] io_slave_awaddr,
  (* keep *) input  [2:0]  io_slave_awsize,
  (* keep *) input  [3:0]  io_slave_awid,
  (* keep *) input  [7:0]  io_slave_awlen,
  (* keep *) input  [1:0]  io_slave_awburst,
  (* keep *) output        io_slave_wready,
  (* keep *) input         io_slave_wvalid,
  (* keep *) input  [31:0] io_slave_wdata,
  (* keep *) input  [3:0]  io_slave_wstrb,
  (* keep *) input         io_slave_wlast,
  (* keep *)               io_slave_bready,
  (* keep *) output        io_slave_bvalid,
  (* keep *) output [1:0]  io_slave_bresp,
  (* keep *) output [3:0]  io_slave_bid,
  (* keep *) output        io_slave_arready,
  (* keep *) input         io_slave_arvalid,
  (* keep *) input  [31:0] io_slave_araddr,
  (* keep *) input  [2:0]  io_slave_arsize,
  (* keep *) input  [3:0]  io_slave_arid,
  (* keep *) input  [7:0]  io_slave_arlen,
  (* keep *) input  [1:0]  io_slave_arburst,
  (* keep *) input         io_slave_rready,
  (* keep *) output        io_slave_rvalid,
  (* keep *) output [1:0]  io_slave_rresp,
  (* keep *) output [31:0] io_slave_rdata,
  (* keep *) output        io_slave_rlast,
  (* keep *) output [3:0]  io_slave_rid
);

  rv_top u_minirv (
    .clock                  (clock),
    .reset                  (reset),
    
    .io_master_awready      (io_master_awready),
    .io_master_awvalid      (io_master_awvalid),
    .io_master_awaddr       (io_master_awaddr),
    .io_master_awsize       (io_master_awsize),
    .io_master_awid         (io_master_awid),
    .io_master_awlen        (io_master_awlen),
    .io_master_awburst      (io_master_awburst),
    .io_master_wready       (io_master_wready),
    .io_master_wvalid       (io_master_wvalid),
    .io_master_wdata        (io_master_wdata),
    .io_master_wstrb        (io_master_wstrb),
    .io_master_wlast        (io_master_wlast),
    .io_master_bready       (io_master_bready),
    .io_master_bvalid       (io_master_bvalid),
    .io_master_bresp        (io_master_bresp),
    .io_master_bid          (io_master_bid),
    .io_master_arready      (io_master_arready),
    .io_master_arvalid      (io_master_arvalid),
    .io_master_araddr       (io_master_araddr),
    .io_master_arsize       (io_master_arsize),
    .io_master_arid         (io_master_arid),
    .io_master_arlen        (io_master_arlen),
    .io_master_arburst      (io_master_arburst),
    .io_master_rready       (io_master_rready),
    .io_master_rvalid       (io_master_rvalid),
    .io_master_rresp        (io_master_rresp),
    .io_master_rdata        (io_master_rdata),
    .io_master_rlast        (io_master_rlast),
    .io_master_rid          (io_master_rid),
    
    .io_interrupt           (io_interrupt),
    
    .io_slave_awready       (io_slave_awready),
    .io_slave_awvalid       (io_slave_awvalid),
    .io_slave_awaddr        (io_slave_awaddr),
    .io_slave_awsize        (io_slave_awsize),
    .io_slave_awid          (io_slave_awid),
    .io_slave_awlen         (io_slave_awlen),
    .io_slave_awburst       (io_slave_awburst),
    .io_slave_wready        (io_slave_wready),
    .io_slave_wvalid        (io_slave_wvalid),
    .io_slave_wdata         (io_slave_wdata),
    .io_slave_wstrb         (io_slave_wstrb),
    .io_slave_wlast         (io_slave_wlast),
    .io_slave_bready        (io_slave_bready),
    .io_slave_bvalid        (io_slave_bvalid),
    .io_slave_bresp         (io_slave_bresp),
    .io_slave_bid           (io_slave_bid),
    .io_slave_arready       (io_slave_arready),
    .io_slave_arvalid       (io_slave_arvalid),
    .io_slave_araddr        (io_slave_araddr),
    .io_slave_arsize        (io_slave_arsize),
    .io_slave_arid          (io_slave_arid),
    .io_slave_arlen         (io_slave_arlen),
    .io_slave_arburst       (io_slave_arburst),
    .io_slave_rready        (io_slave_rready),
    .io_slave_rvalid        (io_slave_rvalid),
    .io_slave_rresp         (io_slave_rresp),
    .io_slave_rdata         (io_slave_rdata),
    .io_slave_rlast         (io_slave_rlast),
    .io_slave_rid           (io_slave_rid)
  );

endmodule

