#!/usr/bin/env python3
"""test flow"""

import logging

from rtl2gds import Chip, step
from rtl2gds.flow import cloud_step
from rtl2gds.global_configs import R2G_BASE_DIR, StepName


def run_gds_dump(chip: Chip):

    step.layout_gds.run(
        top_name=chip.top_name,
        input_def=chip.path_setting.def_file,
        die_bbox=chip.constrain.die_bbox,
        gds_file=chip.path_setting.gds_file,
        snapshot_file=chip.path_setting.gds_file.replace(".gds", ".png"),
        tool="ieda",
    )


def main():
    """retroSoC + rtl2gds flow"""

    logging.basicConfig(
        format="[%(asctime)s - %(levelname)s - %(name)s]: %(message)s",
        level=logging.DEBUG,
        force=True,
    )

    # retrosoc = Chip(f"{R2G_BASE_DIR}/design_zoo/retroSoC/config.yaml")

    # cloud_step.run(chip=retrosoc, expect_step=StepName.SYNTHESIS)
    # cloud_step.run(chip=retrosoc, expect_step=StepName.FLOORPLAN)
    # runtime_log, step_reproducible, subprocess_metrics = step.Step("layout_json_fp").run(
    #     parameters={
    #         "INPUT_DEF": retrosoc.path_setting.def_file,
    #         "RESULT_DIR": retrosoc.path_setting.result_dir,
    #     },
    #     output_prefix="retrosoc",
    # )
    # # compress LAYOUT_JSON_FILE to tar.gz file
    # import tarfile
    # with tarfile.open(f"{R2G_BASE_DIR}/design_zoo/retroSoC/results/retrosoc_asic_placement.tar.gz", "w:gz") as tar:
    #     tar.add(f"{R2G_BASE_DIR}/design_zoo/retroSoC/results/retrosoc_asic_placement.json", arcname="retrosoc_asic_placement.json")
    # print(runtime_log)
    # print(step_reproducible)
    # print(subprocess_metrics)
    # cloud_step.run(chip=retrosoc, expect_step=StepName.PLACEMENT)
    # cloud_step.run(chip=retrosoc, expect_step=StepName.CTS)
    # cloud_step.run(chip=retrosoc, expect_step=StepName.ROUTING)
    retrosoc = Chip(f"{R2G_BASE_DIR}/design_zoo/retroSoC/results/retrosoc_asic_routing.yaml")
    cloud_step.run(chip=retrosoc, expect_step=StepName.SIGNOFF)


if __name__ == "__main__":
    main()
