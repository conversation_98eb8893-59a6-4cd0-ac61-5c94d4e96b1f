# design_setting:
TOP_NAME: retrosoc_asic

# path_setting:
RTL_FILE: ./minirv.sv
# SV_LIST can be:
# - A single file path: ./my_files.f
# - A list of files: [./file1.sv, ./file2.sv]
# - "enable" for legacy retroSoC template mode (deprecated)
SV_LIST: enable  # Legacy mode - will generate retroSoC template filelist
RESULT_DIR: ./results

# constrain:
CLK_PORT_NAME: extclk_i_pad
CLK_FREQ_MHZ: 100

# you can set DIE_AREA and CORE_AREA instead of CORE_UTIL
USE_FIXED_BBOX: true
DIE_BBOX: 0  0 2000 2000
CORE_BBOX: 160 160 1840 1840
