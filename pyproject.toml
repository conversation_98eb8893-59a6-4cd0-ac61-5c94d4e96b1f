[project]
name = "rtl2gds"
version = "0.0.1"
authors = [{ name = "<PERSON> Wizard", email = "<EMAIL>" }]
description = "A tool to compile your RTL files into GDSII layouts."
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "License :: OSI Approved :: MIT License",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.10",
]
dependencies = [
    "pyyaml==6.0.2",
    "orjson==3.10.18",
    "klayout==0.30.2",
    "IPython==8.37.0",
    "cairocffi==1.7.1",
    "cffi==1.17.1",
    "cycler==0.12.1",
    "easyprocess==1.1",
    "entrypoint2==1.1",
    "filelock==3.18.0",
    "fsspec==2025.5.1",
    "jinja2==3.1.6",
    "kiwisolver==1.4.8",
    "markupsafe==3.0.2",
    "matplotlib==3.1.1",
    "mpmath==1.3.0",
    "networkx==3.4.2",
    "numpy==1.23.5",
    "nvidia-cublas-cu12==********",
    "nvidia-cuda-cupti-cu12==12.6.80",
    "nvidia-cuda-nvrtc-cu12==12.6.77",
    "nvidia-cuda-runtime-cu12==12.6.77",
    "nvidia-cudnn-cu12==********",
    "nvidia-cufft-cu12==********",
    "nvidia-cufile-cu12==********",
    "nvidia-curand-cu12==*********",
    "nvidia-cusolver-cu12==********",
    "nvidia-cusparse-cu12==********",
    "nvidia-cusparselt-cu12==0.6.3",
    "nvidia-nccl-cu12==2.26.2",
    "nvidia-nvjitlink-cu12==12.6.85",
    "nvidia-nvtx-cu12==12.6.77",
    "patool==4.0.1",
    "pillow==11.3.0",
    "pip==25.1",
    "pkgconfig==1.5.5",
    "pycparser==2.22",
    "pyparsing==3.2.3",
    "pyqt5==5.15.11",
    "pyqt5-qt5==5.15.17",
    "pyqt5-sip==12.17.0",
    "pyro4==4.82",
    "python-dateutil==2.9.0.post0",
    "pytz==2025.2",
    "pyunpack==0.3",
    "rtree==1.0.1",
    "serpent==1.41",
    "setuptools==78.1.1",
    "shapely==2.1.1",
    "six==1.17.0",
    "sympy==1.14.0",
    "torch==2.7.1",
    "torchvision==0.22.1",
    "triton==3.3.1",
    "wheel==0.45.1",
]

[project.optional-dependencies]
cloud = [
    "requests>=2.32.4",
    "uvicorn>=0.34.3",
    "fastapi[standard]",
    "tenacity"
]

[tool.pylint]
extension-pkg-whitelist = ["orjson"]

[project.urls]
Homepage = "https://atomgit.com/harrywh/rtl2gds"
Issues = "https://atomgit.com/harrywh/rtl2gds/issues"
Documentation = "https://atomgit.com/harrywh/rtl2gds"
Repository = "https://atomgit.com/harrywh/rtl2gds"

[project.scripts]
rtl2gds = "rtl2gds.__main__:main"

[tool.setuptools]
package-dir = {"" = "src"}
packages = ["rtl2gds", "rtl2gds.chip", "rtl2gds.flow", "rtl2gds.step", "rtl2gds.utils"]

[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"

[tool.black]
line-length = 100
target-version = ['py310', 'py312']
include = '\.pyi?$'
exclude = '''
/(
    \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | bin
    | _build
    | buck-out
    | build
    | dist
    | foundry
    | demo
    | docs
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_gitignore = true
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
known_first_party = ["rtl2gds"]
skip = ["venv", "bin", "build", "dist", "foundry", "third_party"]
