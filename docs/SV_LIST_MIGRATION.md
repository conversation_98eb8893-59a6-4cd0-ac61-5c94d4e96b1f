# SV_LIST Parameter Migration Guide

## Overview

The `SV_LIST` parameter in RTL2GDS has been refactored to properly handle SystemVerilog file paths instead of just being an "enable" flag. This change provides better flexibility and aligns with the actual usage in the synthesis workflow.

## What Changed

### Before (Legacy)
```yaml
SV_LIST: enable  # Simple enable flag
```

### After (New)
```yaml
# Single file
SV_LIST: ./path/to/file.sv

# Multiple files
SV_LIST:
  - ./path/to/file1.sv
  - ./path/to/file2.sv

# Filelist file
SV_LIST: ./path/to/files.f

# Legacy mode (still supported)
SV_LIST: enable
```

## Migration Guide

### 1. Single SystemVerilog File
If you have a single SystemVerilog file, specify its path directly:

```yaml
TOP_NAME: my_design
RTL_FILE: ./src/my_design.v
SV_LIST: ./src/my_design.sv  # Direct file path
```

### 2. Multiple SystemVerilog Files
For multiple files, use a YAML list:

```yaml
TOP_NAME: my_design
RTL_FILE: ./src/my_design.v
SV_LIST:
  - ./src/module1.sv
  - ./src/module2.sv
  - ./src/interfaces.sv
```

### 3. Using Filelist Files
If you have a filelist file (`.f` file), specify its path:

```yaml
TOP_NAME: my_design
RTL_FILE: ./src/my_design.v
SV_LIST: ./src/files.f
```

### 4. No SystemVerilog Files
If you don't use SystemVerilog files, simply omit the `SV_LIST` parameter:

```yaml
TOP_NAME: my_design
RTL_FILE: ./src/my_design.v
# No SV_LIST needed
```

### 5. Legacy retroSoC Mode
For backward compatibility, the legacy "enable" flag is still supported:

```yaml
TOP_NAME: retrosoc_asic
RTL_FILE: ./minirv.sv
SV_LIST: enable  # Legacy mode - generates retroSoC template
```

## Technical Details

### How It Works
1. **File Path Mode**: When `SV_LIST` contains actual file paths, they are passed directly to the yosys synthesis script
2. **Legacy Mode**: When `SV_LIST` is "enable", "true", or "1", the system generates a retroSoC template filelist
3. **Validation**: All specified files are validated to exist during chip initialization

### Synthesis Integration
The `SV_LIST` parameter is used in the yosys synthesis script as:
```tcl
yosys read_slang -F $sv_list --top $top_design
```

Where `$sv_list` now contains:
- Direct file path for single files
- Path to generated filelist for multiple files
- Path to retroSoC template filelist for legacy mode

## Error Handling

The system now provides better error messages:

```python
# File not found error
FileNotFoundError: SV_LIST file not found: /path/to/missing.sv

# Multiple files validation
FileNotFoundError: SV_LIST file not found: /path/to/missing2.sv
```

## Backward Compatibility

- Legacy `SV_LIST: enable` configurations continue to work
- Existing retroSoC workflows are unaffected
- No changes required for projects not using SystemVerilog

## Best Practices

1. **Use absolute paths** or paths relative to the config file location
2. **Validate file existence** before running synthesis
3. **Use filelist files** for large numbers of SystemVerilog files
4. **Migrate from legacy mode** to explicit file paths for better control

## Examples

See `examples/sv_list_usage.yaml` for complete configuration examples demonstrating all usage patterns.
