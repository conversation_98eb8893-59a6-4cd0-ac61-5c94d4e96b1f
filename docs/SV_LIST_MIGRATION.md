# SV_LIST Parameter Migration Guide

## Overview

The `SV_LIST` parameter in RTL2GDS has been refactored to properly handle filelist file paths instead of just being an "enable" flag. This change aligns with standard VLSI design workflows where `SV_LIST` should point to a filelist file (`.f` or `.list`) containing SystemVerilog source files.

## What Changed

### Before (Legacy)
```yaml
SV_LIST: enable  # Simple enable flag
```

### After (New)
```yaml
# Filelist file (recommended)
SV_LIST: ./path/to/sources.f

# Legacy mode (still supported)
SV_LIST: enable
```

## Important: Filelist File Format

The `SV_LIST` parameter should point to a **filelist file** (commonly with `.f` or `.list` extension) that contains a list of SystemVerilog source files, one per line. This is the standard approach used in VLSI design workflows.

Example filelist file (`sources.f`):
```
+incdir+./src/includes
./src/interfaces/axi_if.sv
./src/interfaces/memory_if.sv
./src/modules/cpu_core.sv
./src/modules/cache_controller.sv
./src/packages/cpu_pkg.sv
```

## Migration Guide

### 1. Create a Filelist File
Create a filelist file (e.g., `sources.f`) containing your SystemVerilog files:

```
# sources.f
+incdir+./src/includes
./src/interfaces/axi_if.sv
./src/interfaces/memory_if.sv
./src/modules/cpu_core.sv
./src/packages/cpu_pkg.sv
```

### 2. Update Configuration
Point `SV_LIST` to your filelist file:

```yaml
TOP_NAME: my_design
RTL_FILE: ./src/my_design.v
SV_LIST: ./src/sources.f  # Path to filelist file
CLK_PORT_NAME: clk
CLK_FREQ_MHZ: 100
RESULT_DIR: ./results
```

### 3. No SystemVerilog Files
If you don't use SystemVerilog files, simply omit the `SV_LIST` parameter:

```yaml
TOP_NAME: my_design
RTL_FILE: ./src/my_design.v
# No SV_LIST needed for Verilog-only designs
CLK_PORT_NAME: clk
CLK_FREQ_MHZ: 100
RESULT_DIR: ./results
```

### 4. Legacy retroSoC Mode
For backward compatibility, the legacy "enable" flag is still supported:

```yaml
TOP_NAME: retrosoc_asic
RTL_FILE: ./minirv.sv
SV_LIST: enable  # Legacy mode - generates retroSoC template
CLK_PORT_NAME: extclk_i_pad
CLK_FREQ_MHZ: 100
RESULT_DIR: ./results
```

## Technical Details

### How It Works
1. **Filelist Mode**: When `SV_LIST` contains a path to a filelist file, it is passed directly to the yosys synthesis script
2. **Legacy Mode**: When `SV_LIST` is "enable", "true", or "1", the system generates a retroSoC template filelist
3. **Validation**: The filelist file is validated to exist and be readable during chip initialization

### Synthesis Integration
The `SV_LIST` parameter is used in the yosys synthesis script as:
```tcl
yosys read_slang -F $sv_list --top $top_design
```

The `-F` flag tells yosys to read from a filelist file, where `$sv_list` contains:
- Path to user-provided filelist file (recommended)
- Path to generated retroSoC template filelist (legacy mode)

### Filelist File Format
Filelist files support standard VLSI design file formats:
- One file path per line
- Include directories: `+incdir+./path/to/includes`
- Comments: Lines starting with `#` or `//`
- Relative and absolute paths

## Error Handling

The system now provides better error messages:

```python
# Filelist file not found
FileNotFoundError: SV_LIST filelist file not found: /path/to/missing.f

# Invalid file type
ValueError: SV_LIST must be a string path to a filelist file, got <class 'list'>

# File not readable
ValueError: SV_LIST filelist file is not readable: /path/to/file.f - Permission denied
```

## Backward Compatibility

- Legacy `SV_LIST: enable` configurations continue to work
- Existing retroSoC workflows are unaffected
- No changes required for projects not using SystemVerilog

## Best Practices

1. **Use filelist files** for all SystemVerilog sources (industry standard)
2. **Use absolute paths** or paths relative to the config file location in filelist files
3. **Organize filelist files** by grouping related sources (interfaces, packages, modules)
4. **Include directories** using `+incdir+` syntax in filelist files
5. **Validate filelist content** before running synthesis
6. **Migrate from legacy mode** to explicit filelist files for better control

## Common Filelist Patterns

```
# Basic filelist structure
+incdir+./src/includes
+incdir+./src/packages

# Package files first
./src/packages/cpu_pkg.sv
./src/packages/memory_pkg.sv

# Interface files
./src/interfaces/axi_if.sv
./src/interfaces/memory_if.sv

# Module files
./src/modules/cpu_core.sv
./src/modules/cache_controller.sv
./src/modules/memory_controller.sv
```

## Examples

See `examples/sv_list_usage.yaml` for complete configuration examples demonstrating all usage patterns.
